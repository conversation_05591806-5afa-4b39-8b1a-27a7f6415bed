# 医学影像数据集使用指南

本文档介绍如何使用新实现的 `MedicalImageDataset` 类来加载和处理 nii.gz 格式的医学影像数据，用于MR序列分类任务。

## 功能特性

- 支持 nii.gz 和 nii 格式的医学影像文件
- 使用 SimpleITK 读取医学影像数据
- **3通道切片堆叠**: 将三个切面堆叠成RGB图像，充分利用预训练CNN模型
- 支持两种切片提取模式：
  - **middle模式**: 提取 xyz 面的中间切片（轴向面+冠状面+矢状面）
  - **multi模式**: 提取指定轴的 25%、50%、75% 位置切片
- 自动标签解析（支持目录结构和文件名解析）
- 标准化图像预处理和尺寸调整
- 与 PyTorch DataLoader 完全兼容
- 专门的预测工具，支持单文件和批量预测

## 数据目录结构

### 方式1：按目录分类
```
data/medical/
├── train/
│   ├── t1/
│   │   ├── patient001_t1.nii.gz
│   │   ├── patient002_t1.nii.gz
│   │   └── ...
│   ├── t2/
│   │   ├── patient001_t2.nii.gz
│   │   ├── patient002_t2.nii.gz
│   │   └── ...
│   ├── flair/
│   │   └── ...
│   └── dwi/
│       └── ...
├── val/
│   └── (同样的结构)
└── test/
    └── (同样的结构)
```

### 方式2：文件名包含标签
```
data/medical/
├── train/
│   ├── patient001_t1.nii.gz
│   ├── patient001_t2.nii.gz
│   ├── patient002_flair.nii.gz
│   ├── patient002_dwi.nii.gz
│   └── ...
├── val/
│   └── ...
└── test/
    └── ...
```

## 基本使用方法

### 1. 导入必要的模块

```python
from src.data_loader import MedicalImageDataset, get_medical_data_loaders
from config.config import Config
from torchvision import transforms
```

### 2. 创建数据集实例

```python
# 配置数据变换
transform = transforms.Compose([
    transforms.Resize((224, 224)),
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                        std=[0.229, 0.224, 0.225])
])

# 创建数据集 - middle模式
dataset = MedicalImageDataset(
    data_dir="data/medical/train",
    transform=transform,
    slice_mode='middle',  # 提取xyz面中间切片
    axis='z'  # 在middle模式下此参数无效
)

# 创建数据集 - multi模式
dataset_multi = MedicalImageDataset(
    data_dir="data/medical/train",
    transform=transform,
    slice_mode='multi',   # 提取多个切片
    axis='z'             # 在z轴上提取25%、50%、75%位置
)
```

### 3. 使用便捷函数创建数据加载器

```python
config = Config()

# 创建医学影像数据加载器
data_loaders, class_names, dataset_sizes = get_medical_data_loaders(
    config, 
    slice_mode='middle',  # 或 'multi'
    axis='z'             # 当slice_mode='multi'时有效
)

# 获取训练数据加载器
train_loader = data_loaders['train']
val_loader = data_loaders['val']
```

## 切片模式详解

### 3通道堆叠原理

本实现的核心特性是将三个切片堆叠成3通道RGB图像，这样做的优势：

1. **充分利用预训练模型**: 预训练的CNN模型（如ResNet、EfficientNet）期望3通道输入
2. **多视角信息融合**: 同时包含不同方向或位置的解剖信息
3. **提高分类性能**: 比单一切片包含更丰富的特征信息

### Middle模式（推荐）
提取三个正交面的中间切片并堆叠：
- **R通道**: 轴向面 (Axial) - Z轴中间位置切片
- **G通道**: 冠状面 (Coronal) - Y轴中间位置切片
- **B通道**: 矢状面 (Sagittal) - X轴中间位置切片

```python
dataset = MedicalImageDataset(
    data_dir="path/to/data",
    slice_mode='middle'  # 输出3通道RGB图像
)

# 获取样本
image, label = dataset[0]
print(f"图像模式: {image.mode}")  # 输出: RGB
print(f"图像尺寸: {image.size}")  # 例如: (224, 224)
```

### Multi模式
在指定轴向上提取多个位置的切片并堆叠：
- **R通道**: 25% 位置切片
- **G通道**: 50% 位置切片（中间）
- **B通道**: 75% 位置切片

```python
# Z轴多切片 - 适合分析轴向变化
dataset_z = MedicalImageDataset(
    data_dir="path/to/data",
    slice_mode='multi',
    axis='z'  # 在Z轴上取3个不同位置
)

# Y轴多切片 - 适合分析前后变化
dataset_y = MedicalImageDataset(
    data_dir="path/to/data",
    slice_mode='multi',
    axis='y'
)

# X轴多切片 - 适合分析左右变化
dataset_x = MedicalImageDataset(
    data_dir="path/to/data",
    slice_mode='multi',
    axis='x'
)
```

### 切片堆叠处理流程

1. **切片提取**: 根据模式提取3个2D切片
2. **尺寸统一**: 将不同尺寸的切片调整到相同大小
3. **强度标准化**: 每个切片独立标准化到0-255范围
4. **通道堆叠**: 将3个切片作为R、G、B通道合成RGB图像
5. **预处理**: 应用resize、数据增强、标准化等变换

## 训练示例

### 使用专门的训练脚本

```bash
# 运行医学影像训练脚本
python src/train_medical.py
```

### 自定义训练循环

```python
from src.train_medical import train_medical_model
from config.config import Config

config = Config()

# 训练模型
model = train_medical_model(
    config,
    slice_mode='middle',  # 或 'multi'
    axis='z',            # 当slice_mode='multi'时指定轴向
    model_name='resnet50' # 或 'resnet18'
)
```

## 数据可视化

使用示例脚本可视化数据集：

```bash
# 运行可视化示例
python examples/medical_dataset_example.py
```

这将创建示例数据目录结构并测试数据加载功能。

## 模型预测

### 使用预测器类

```python
from src.predict_medical import MedicalImagePredictor

# 创建预测器
predictor = MedicalImagePredictor(
    model_path="checkpoints/resnet50_middle_z_best.pth",
    slice_mode='middle',  # 与训练时保持一致
    axis='z'
)

# 预测单个文件
result = predictor.predict("path/to/test.nii.gz", return_probabilities=True)
print(f"预测序列: {result['predicted_class']}")
print(f"置信度: {result['confidence']:.3f}")
print(f"概率分布: {result['probabilities']}")

# 批量预测
nii_files = ["test1.nii.gz", "test2.nii.gz", "test3.nii.gz"]
results = predictor.predict_batch(nii_files, return_probabilities=True)
```

### 命令行预测

```bash
# 预测单个文件
python src/predict_medical.py \
    --model checkpoints/resnet50_middle_z_best.pth \
    --input data/test/sample.nii.gz \
    --slice_mode middle \
    --probabilities

# 使用multi模式预测
python src/predict_medical.py \
    --model checkpoints/resnet50_multi_z_best.pth \
    --input data/test/sample.nii.gz \
    --slice_mode multi \
    --axis z \
    --probabilities
```

### 预测结果格式

```python
{
    'file_path': 'path/to/test.nii.gz',
    'predicted_class': 't1',
    'predicted_class_idx': 0,
    'confidence': 0.892,
    'slice_mode': 'middle',
    'axis': None,
    'probabilities': {
        't1': 0.892,
        't2': 0.065,
        'flair': 0.032,
        'dwi': 0.011
    }
}
```

## 配置参数

在 `config/config.py` 中可以调整以下参数：

```python
# 医学影像特定参数
SLICE_MODE = 'middle'  # 'middle' 或 'multi'
SLICE_AXIS = 'z'       # 当SLICE_MODE='multi'时使用
NUM_CLASSES = 4        # MR序列类别数量
MR_SEQUENCES = ['t1', 't2', 'flair', 'dwi']  # 序列类型

# 数据路径
MEDICAL_DATA_DIR = "data/medical"

# 训练参数
BATCH_SIZE = 32
IMAGE_SIZE = 224
NUM_EPOCHS = 25
LEARNING_RATE = 1e-4
```

## 注意事项

1. **数据格式**: 确保医学影像文件为 nii.gz 或 nii 格式
2. **内存使用**: 大型医学影像文件可能占用较多内存，建议适当调整批次大小
3. **标签命名**: 确保标签名称一致，支持的序列类型包括 t1、t2、flair、dwi 等
4. **数据预处理**: 图像会自动标准化到 0-255 范围，并应用指定的变换
5. **错误处理**: 如果文件读取失败，会返回默认的黑色图像并打印错误信息

## 故障排除

### 常见问题

1. **ImportError: No module named 'SimpleITK'**
   ```bash
   pip install SimpleITK
   ```

2. **数据集为空**
   - 检查数据目录路径是否正确
   - 确认文件扩展名为 .nii.gz 或 .nii
   - 验证目录结构是否符合要求

3. **内存不足**
   - 减少批次大小 (BATCH_SIZE)
   - 减少工作进程数 (NUM_WORKERS)

4. **标签解析错误**
   - 检查目录名称或文件名格式
   - 确保标签名称不包含特殊字符

## 扩展功能

可以根据需要扩展以下功能：
- 支持更多医学影像格式 (DICOM等)
- 添加更多数据增强方法
- 实现多切片融合策略
- 支持3D卷积网络
- 添加影像质量评估
