# 动态序列发现功能

## 🎯 概述

动态序列发现功能允许系统自动从 `data/medical/train/` 文件夹结构中识别和搜集MR序列类型，而不需要手动配置固定的序列列表。这大大提高了系统的灵活性和适应性。

## ✨ 主要特性

### 🔍 自动发现
- **智能识别**: 自动扫描数据目录，识别所有可用的序列类型
- **灵活适应**: 支持标准序列（t1, t2, flair, dwi, adc, b0）和自定义序列
- **无需配置**: 不需要手动维护序列类型列表

### 🏗️ 支持的目录结构
```
data/medical/train/
├── t1/
│   ├── sample_001.nii.gz
│   ├── sample_002.nii.gz
│   └── ...
├── t2/
│   ├── sample_001.nii.gz
│   └── ...
├── flair/
│   └── sample_001.nii.gz
├── dwi/
│   └── sample_001.nii.gz
├── adc/
│   └── sample_001.nii.gz
└── [任何其他序列类型]/
    └── *.nii.gz
```

## 🚀 使用方法

### 基本使用

```python
from examples.complete_medical_workflow import discover_sequence_types, analyze_dataset

# 1. 发现序列类型
data_dir = "data/medical/train"
sequences = discover_sequence_types(data_dir)
print(f"发现的序列: {sequences}")

# 2. 完整数据集分析
sequence_counts, file_info, discovered_sequences = analyze_dataset(data_dir)
print(f"序列分布: {sequence_counts}")
```

### 在训练中使用

```python
from src.train_medical_dynamic import train_medical_model_dynamic
from config.config import Config

config = Config()

# 训练模型，自动发现序列类型
model = train_medical_model_dynamic(
    config,
    slice_mode='middle',
    axis='z',
    model_name='resnet18'
)
```

### 在数据加载器中使用

```python
from src.data_loader import MedicalImageDataset

# 数据集会自动识别所有可用的序列类型
dataset = MedicalImageDataset(
    data_dir="data/medical/train",
    slice_mode='middle'
)

print(f"发现的类别: {dataset.get_class_names()}")
```

## 🔧 核心函数

### `discover_sequence_types(data_dir)`

**功能**: 扫描数据目录，发现所有可用的序列类型

**参数**:
- `data_dir`: 数据目录路径

**返回**: 发现的序列类型列表（已排序）

**示例**:
```python
sequences = discover_sequence_types("data/medical/train")
# 输出: ['adc', 'dwi', 'flair', 't1', 't2']
```

### `analyze_dataset(data_dir)`

**功能**: 完整分析数据集，包括序列发现、文件统计和基本信息

**参数**:
- `data_dir`: 数据目录路径

**返回**: 
- `sequence_counts`: 各序列的文件数量
- `file_info`: 文件详细信息列表
- `discovered_sequences`: 发现的序列类型

**示例**:
```python
counts, info, sequences = analyze_dataset("data/medical/train")
# counts: {'t1': 5, 't2': 3, 'flair': 2}
# sequences: ['flair', 't1', 't2']
```

## 📊 演示脚本

### 运行演示
```bash
python demo_dynamic_sequences.py
```

这个脚本会：
1. 显示当前数据目录结构
2. 演示动态序列发现过程
3. 对比固定序列列表的方法
4. 展示完整的数据集分析结果

### 测试功能
```bash
python test_sequence_discovery.py
```

## ⚙️ 配置选项

在 `config/config.py` 中可以配置：

```python
# 动态序列发现配置
AUTO_DISCOVER_SEQUENCES = True  # 是否自动发现序列类型
FALLBACK_SEQUENCES = ['t1', 't2', 'flair', 'dwi', 'adc', 'b0']  # 备用序列列表
```

## 🎯 优势对比

### 传统固定列表方法
```python
# 需要手动配置
SEQUENCES = ['t1', 't2', 'flair', 'dwi', 'adc', 'b0']

# 问题：
# ❌ 新序列类型需要手动添加
# ❌ 不同数据集可能有不同的序列
# ❌ 容易遗漏或配置错误
# ❌ 维护成本高
```

### 动态发现方法
```python
# 自动发现
sequences = discover_sequence_types(data_dir)

# 优势：
# ✅ 自动适应不同数据集
# ✅ 支持任意序列类型
# ✅ 无需手动维护
# ✅ 减少配置错误
# ✅ 提高代码通用性
```

## 🔍 识别规则

系统使用以下规则识别序列类型：

1. **目录名匹配**: 检查包含nii文件的目录名
2. **常见序列模式**: 识别以下模式
   - 标准序列: `t1`, `t2`, `flair`, `dwi`, `adc`, `b0`
   - 扩展序列: `swi`, `pd`, `bold`, `dti`
   - 前缀匹配: 以上述序列开头的目录名

3. **文件名回退**: 如果目录名无法识别，尝试从文件名推断

## 🚨 注意事项

1. **目录结构**: 确保每个序列类型有独立的子目录
2. **文件格式**: 支持 `.nii.gz` 和 `.nii` 格式
3. **命名规范**: 建议使用标准的序列名称以确保正确识别
4. **备用机制**: 如果无法识别，会使用配置中的备用序列列表

## 🔧 故障排除

### 常见问题

**Q: 某些序列没有被识别？**
A: 检查目录名是否符合识别规则，或在配置中添加到备用列表

**Q: 识别了错误的序列类型？**
A: 确保目录结构正确，避免使用容易混淆的目录名

**Q: 系统报告"未发现任何序列类型"？**
A: 检查数据目录路径是否正确，确保包含nii文件的目录存在

### 调试方法

```python
# 启用详细输出
import os
data_dir = "data/medical/train"

# 检查目录结构
for root, dirs, files in os.walk(data_dir):
    nii_files = [f for f in files if f.endswith('.nii.gz') or f.endswith('.nii')]
    if nii_files:
        print(f"目录: {root}")
        print(f"文件: {nii_files[:3]}...")

# 测试发现功能
sequences = discover_sequence_types(data_dir)
print(f"发现结果: {sequences}")
```

## 📈 未来扩展

- 支持更多序列类型的自动识别
- 添加序列类型的置信度评分
- 支持嵌套目录结构
- 集成序列类型验证功能
