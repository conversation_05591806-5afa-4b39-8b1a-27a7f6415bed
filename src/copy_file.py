#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os
import shutil
import json
import argparse
from multiprocessing import Pool
from tqdm import tqdm

# --- 全局常量 ---
# 源文件的根目录，请根据您的实际情况修改
HASH_ROOT = '/data01/arspretraindata/rawfiles'

# --- 多进程相关函数 ---

# 定义一个全局变量，它将在每个子进程中有自己独立的值
worker_save_dir = None

def init_worker(s_dir: str):
    """
    每个工作进程启动时会调用的初始化函数。
    它接收主进程传来的 save_dir，并将其存为当前进程的全局变量。
    """
    global worker_save_dir
    worker_save_dir = s_dir

def copy_file(nphash: str, predict_result: any):
    """
    执行单个文件复制的核心工作函数。
    它不再接收 save_dir 参数，而是直接使用在子进程中初始化好的全局变量。
    """
    try:
        origin_path = os.path.join(HASH_ROOT, nphash[:2], nphash[2:5], nphash, 'image.nii.gz')
        # 直接使用子进程自己的全局变量 worker_save_dir
        save_path = os.path.join(worker_save_dir, f'{nphash}-{predict_result}.nii.gz')

        if os.path.exists(origin_path):
            shutil.copy2(origin_path, save_path)
            return True, origin_path
        else:
            # 如果源文件不存在，可以记录下来
            return False, f"Source file not found: {origin_path}"
    except Exception as e:
        return False, f"Error copying {origin_path}: {e}"


def copy_file_wrapper(args_tuple: tuple):
    """
    包装函数，用于解开 imap 传递过来的元组参数。
    imap 只会给目标函数传递一个参数，所以我们需要这个包装器来调用真正的多参数工作函数。
    """
    return copy_file(*args_tuple)

def run(args):
    """
    主执行函数
    """
    save_dir = args.save_dir
    # 1. 确保目标目录存在，如果不存在则创建
    os.makedirs(save_dir, exist_ok=True)
    print(f"目标目录 '{save_dir}' 已准备就绪。")

    # 2. 从 JSON 文件加载任务列表
    # 假设 JSON 文件格式为: [[nphash1, predict_result1], [nphash2, predict_result2], ...]
    try:
        with open(args.f, 'r') as f:
            task_list = json.load(f)
        print(f"从 '{args.f}' 文件中成功加载 {len(task_list)} 个任务。")
    except (FileNotFoundError, json.JSONDecodeError) as e:
        print(f"错误：无法加载或解析JSON文件 '{args.f}'。 {e}")
        return

    # 3. [OPTIMIZATION] 创建并管理多进程池
    with Pool(processes=args.process,
              initializer=init_worker,
              initargs=(save_dir,)) as pool:

        # 4. [FIX] 使用 tqdm 包装 imap_unordered 以显示实时进度
        print(f"开始使用 {args.process} 个进程进行文件复制...")
        with tqdm(total=len(task_list), desc="文件拷贝进度", unit="个") as pbar:
            for success, message in pool.imap_unordered(copy_file_wrapper, task_list):
                if not success:
                    # 可以在这里处理复制失败的情况，比如打印错误日志
                    print(f"\n[警告] {message}")
                pbar.update(1)

    print("所有任务已完成！")


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='从JSON文件描述中并行复制nii文件。')
    parser.add_argument('-f', type=str, required=True, help='包含nii文件哈希和结果的JSON文件路径')
    parser.add_argument('--process', type=int, default=8, help='使用的进程数量 (默认: 8)')
    parser.add_argument('--save_dir', type=str, required=True, help='复制后文件的保存目录')

    args = parser.parse_args()
    run(args)