import os
import torch
from torch.utils.data import Dataset, DataLoader
from torchvision import datasets, transforms, models
import torch.nn.functional as F
import SimpleITK as sitk
import numpy as np
from PIL import Image

class MedicalImageDataset(Dataset):
    def __init__(self, data_dir, transform=None, slice_mode='middle', axis='z'):
        """
        初始化医学影像数据集。
        Args:
            data_dir (str): 包含nii.gz文件和标签的文件夹路径。
            transform (callable, optional): 应用于图像的变换。
            slice_mode (str): 切片模式，'middle' 或 'multi'
                - 'middle': 取xyz面的中间切片
                - 'multi': 取指定轴的25%、50%、75%位置切片
            axis (str): 当slice_mode='multi'时指定轴向，'x', 'y', 'z'，默认'z'
        """
        self.data_dir = data_dir
        self.transform = transform
        self.slice_mode = slice_mode
        self.axis = axis
        self.image_paths = []
        self.labels = []
        self.label_to_idx = {}

        # 收集所有nii.gz文件和对应标签
        self._load_dataset()

    def _load_dataset(self):
        """加载数据集，支持两种目录结构：
        1. data_dir/label_name/file.nii.gz
        2. data_dir/file_label.nii.gz (从文件名解析标签)
        """
        label_names = set()

        # 遍历数据目录
        for item in os.listdir(self.data_dir):
            item_path = os.path.join(self.data_dir, item)

            if os.path.isdir(item_path):
                # 目录结构：data_dir/label_name/file.nii.gz
                label_name = item
                label_names.add(label_name)

                for file_name in os.listdir(item_path):
                    if file_name.endswith('.nii.gz') or file_name.endswith('.nii'):
                        file_path = os.path.join(item_path, file_name)
                        self.image_paths.append(file_path)
                        self.labels.append(label_name)

            elif item.endswith('.nii.gz') or item.endswith('.nii'):
                # 文件结构：data_dir/file_label.nii.gz
                # 从文件名解析标签（假设格式为：xxx_label.nii.gz）
                file_name = os.path.splitext(os.path.splitext(item)[0])[0]  # 去掉.nii.gz
                parts = file_name.split('_')
                if len(parts) >= 2:
                    label_name = parts[-1]  # 取最后一部分作为标签
                    label_names.add(label_name)
                    self.image_paths.append(item_path)
                    self.labels.append(label_name)

        # 创建标签到索引的映射
        sorted_labels = sorted(list(label_names))
        self.label_to_idx = {label: idx for idx, label in enumerate(sorted_labels)}
        self.idx_to_label = {idx: label for label, idx in self.label_to_idx.items()}

        # 将标签转换为数字索引
        self.labels = [self.label_to_idx[label] for label in self.labels]

        print(f"找到 {len(self.image_paths)} 个医学影像文件")
        print(f"标签类别: {sorted_labels}")
        print(f"各类别数量: {dict(zip(sorted_labels, [self.labels.count(i) for i in range(len(sorted_labels))]))}")

    def _extract_slices(self, nii_path):
        """从nii.gz文件提取切片"""
        try:
            # 读取医学影像
            image = sitk.ReadImage(nii_path)
            arr = sitk.GetArrayFromImage(image)  # 形状通常是 (depth, height, width)

            if self.slice_mode == 'middle':
                return self._extract_middle_slices(arr)
            elif self.slice_mode == 'multi':
                return self._extract_multi_slices(arr)
            else:
                raise ValueError(f"不支持的切片模式: {self.slice_mode}")

        except Exception as e:
            print(f"读取文件 {nii_path} 时出错: {e}")
            # 返回默认的黑色图像
            return [Image.new('L', (224, 224), 0)]

    def _extract_middle_slices(self, arr):
        """提取xyz面的中间切片并堆叠成3通道"""
        depth, height, width = arr.shape

        # Z轴中间切片 (轴向面)
        z_middle = depth // 2
        axial_slice = arr[z_middle, :, :]

        # Y轴中间切片 (冠状面)
        y_middle = height // 2
        coronal_slice = arr[:, y_middle, :]

        # X轴中间切片 (矢状面)
        x_middle = width // 2
        sagittal_slice = arr[:, :, x_middle]

        # 将三个切片堆叠成3通道图像
        stacked_image = self._stack_slices_to_rgb(axial_slice, coronal_slice, sagittal_slice)

        return [stacked_image]  # 返回单个3通道图像

    def _extract_multi_slices(self, arr):
        """提取指定轴的25%、50%、75%位置切片并堆叠成3通道"""
        depth, height, width = arr.shape

        if self.axis == 'z':
            positions = [int(depth * 0.25), int(depth * 0.5), int(depth * 0.75)]
            slice1 = arr[min(positions[0], depth - 1), :, :]
            slice2 = arr[min(positions[1], depth - 1), :, :]
            slice3 = arr[min(positions[2], depth - 1), :, :]

        elif self.axis == 'y':
            positions = [int(height * 0.25), int(height * 0.5), int(height * 0.75)]
            slice1 = arr[:, min(positions[0], height - 1), :]
            slice2 = arr[:, min(positions[1], height - 1), :]
            slice3 = arr[:, min(positions[2], height - 1), :]

        elif self.axis == 'x':
            positions = [int(width * 0.25), int(width * 0.5), int(width * 0.75)]
            slice1 = arr[:, :, min(positions[0], width - 1)]
            slice2 = arr[:, :, min(positions[1], width - 1)]
            slice3 = arr[:, :, min(positions[2], width - 1)]
        else:
            raise ValueError(f"不支持的轴向: {self.axis}")

        # 将三个切片堆叠成3通道图像
        stacked_image = self._stack_slices_to_rgb(slice1, slice2, slice3)

        return [stacked_image]  # 返回单个3通道图像

    def _array_to_pil(self, arr):
        """将numpy数组转换为PIL图像"""
        # 标准化到0-255范围
        arr_normalized = ((arr - arr.min()) / (arr.max() - arr.min() + 1e-8) * 255).astype(np.uint8)
        return Image.fromarray(arr_normalized, mode='L')

    def _normalize_slice(self, arr):
        """标准化单个切片到0-255范围"""
        arr_min, arr_max = arr.min(), arr.max()
        if arr_max > arr_min:
            return ((arr - arr_min) / (arr_max - arr_min) * 255).astype(np.uint8)
        else:
            return np.zeros_like(arr, dtype=np.uint8)

    def _stack_slices_to_rgb(self, slice1, slice2, slice3, target_size=224):
        """将三个切片堆叠成RGB图像，并调整到目标尺寸"""
        # 确保所有切片具有相同的尺寸
        target_shape = slice1.shape

        # 如果切片尺寸不同，需要调整到相同尺寸
        if slice2.shape != target_shape:
            slice2 = self._resize_slice(slice2, target_shape)
        if slice3.shape != target_shape:
            slice3 = self._resize_slice(slice3, target_shape)

        # 标准化每个切片
        slice1_norm = self._normalize_slice(slice1)
        slice2_norm = self._normalize_slice(slice2)
        slice3_norm = self._normalize_slice(slice3)

        # 堆叠成RGB图像
        rgb_array = np.stack([slice1_norm, slice2_norm, slice3_norm], axis=-1)

        # 转换为PIL图像
        pil_image = Image.fromarray(rgb_array, mode='RGB')

        # 调整到目标尺寸，保持纵横比
        pil_image = self._resize_with_padding(pil_image, target_size)

        return pil_image

    def _resize_with_padding(self, image, target_size):
        """调整图像尺寸并保持纵横比，使用填充"""
        w, h = image.size

        # 计算缩放比例
        scale = min(target_size / w, target_size / h)
        new_w, new_h = int(w * scale), int(h * scale)

        # 缩放图像
        resized_image = image.resize((new_w, new_h), Image.LANCZOS)

        # 创建目标尺寸的背景图像
        new_image = Image.new('RGB', (target_size, target_size), (0, 0, 0))

        # 计算粘贴位置（居中）
        paste_x = (target_size - new_w) // 2
        paste_y = (target_size - new_h) // 2

        # 粘贴缩放后的图像
        new_image.paste(resized_image, (paste_x, paste_y))

        return new_image

    def _resize_slice(self, slice_arr, target_shape):
        """调整切片尺寸"""
        from scipy import ndimage
        zoom_factors = [target_shape[i] / slice_arr.shape[i] for i in range(len(target_shape))]
        return ndimage.zoom(slice_arr, zoom_factors, order=1)

    def __len__(self):
        return len(self.image_paths)

    def __getitem__(self, idx):
        nii_path = self.image_paths[idx]
        label = self.labels[idx]

        # 提取切片（现在返回的是3通道RGB图像）
        slices = self._extract_slices(nii_path)

        # 获取3通道堆叠图像
        image = slices[0]  # 现在slices[0]是3通道RGB图像

        if self.transform:
            image = self.transform(image)

        return image, label

    def get_class_names(self):
        """返回类别名称列表"""
        return [self.idx_to_label[i] for i in range(len(self.label_to_idx))]


def get_data_loaders(config, slice_mode='middle', axis='z', use_medical_dataset=True):
    """
    创建并返回训练、验证和测试数据加载器。

    Args:
        config: 配置对象
        slice_mode (str): 切片模式，'middle' 或 'multi'
        axis (str): 当slice_mode='multi'时指定轴向，'x', 'y', 'z'
        use_medical_dataset (bool): 是否使用医学影像数据集
    """
    # 定义数据增强和预处理
    # 注意：不需要PadToSquare，因为在_stack_slices_to_rgb中已经处理了尺寸统一
    data_transforms = {
        "train": transforms.Compose([
            transforms.RandomHorizontalFlip(),  # 数据增强 - 水平翻转
            transforms.RandomRotation(10),  # 数据增强 - 随机旋转
            transforms.ToTensor(),  # 转为Tensor (值在0-1之间)
            transforms.Normalize(mean=config.IMAGENET_MEAN, std=config.IMAGENET_STD)  # 标准化
        ]),
        # 验证/测试集的预处理 (通常不做数据增强)
        "val": transforms.Compose([
            transforms.ToTensor(),
            transforms.Normalize(mean=config.IMAGENET_MEAN, std=config.IMAGENET_STD)
        ]),
        "test": transforms.Compose([
            transforms.ToTensor(),
            transforms.Normalize(mean=config.IMAGENET_MEAN, std=config.IMAGENET_STD)
        ])}

    if use_medical_dataset:
        # 使用医学影像数据集
        image_datasets = {}
        for split in ['train', 'val', 'test']:
            split_dir = os.path.join(config.DATA_DIR, split)
            if os.path.exists(split_dir):
                image_datasets[split] = MedicalImageDataset(
                    data_dir=split_dir,
                    transform=data_transforms[split],
                    slice_mode=slice_mode,
                    axis=axis
                )
            else:
                print(f"警告: 目录 {split_dir} 不存在，跳过 {split} 数据集")
    else:
        # 使用传统的ImageFolder数据集
        image_datasets = {x: datasets.ImageFolder(root=f'{config.DATA_DIR}/{x}', transform=data_transforms[x])
                          for x in ['train', 'val', 'test'] if os.path.exists(f'{config.DATA_DIR}/{x}')}

    # 创建数据加载器
    data_loaders = {}
    for split in ['train', 'val']:
        if split in image_datasets:
            shuffle = (split == 'train')  # 只有训练集需要shuffle
            data_loaders[split] = DataLoader(
                image_datasets[split],
                batch_size=config.BATCH_SIZE,
                shuffle=shuffle,
                num_workers=config.NUM_WORKERS
            )

    # 测试集的 shuffle 通常为 False
    if 'test' in image_datasets:
        data_loaders['test'] = DataLoader(
            image_datasets['test'],
            batch_size=config.BATCH_SIZE,
            shuffle=False,
            num_workers=config.NUM_WORKERS
        )

    dataset_sizes = {x: len(image_datasets[x]) for x in image_datasets.keys()}

    # 获取类别名称
    if use_medical_dataset and 'train' in image_datasets:
        class_names = image_datasets['train'].get_class_names()
    elif not use_medical_dataset and 'train' in image_datasets:
        class_names = image_datasets['train'].classes
    else:
        class_names = []

    num_classes = len(class_names)

    print(f"数据集信息:")
    for split, size in dataset_sizes.items():
        print(f"  {split}集大小: {size}")
    print(f"类别数量: {num_classes}")
    print(f"类别名称: {class_names}")
    print(f"切片模式: {slice_mode}")
    if slice_mode == 'multi':
        print(f"切片轴向: {axis}")

    return data_loaders, class_names, dataset_sizes


def get_medical_data_loaders(config, slice_mode='middle', axis='z'):
    """
    专门用于医学影像的数据加载器创建函数

    Args:
        config: 配置对象
        slice_mode (str): 切片模式
            - 'middle': 取xyz面的中间切片
            - 'multi': 取指定轴的25%、50%、75%位置切片
        axis (str): 当slice_mode='multi'时指定轴向，'x', 'y', 'z'，默认'z'
    """
    return get_data_loaders(config, slice_mode=slice_mode, axis=axis, use_medical_dataset=True)