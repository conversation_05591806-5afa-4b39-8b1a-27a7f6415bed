import torch
from torchvision import models, transforms
from PIL import Image
import os
import torch.nn as nn
from config.config import Config
from src.utils import  PadToSquare
import argparse

def predict_image(image_path, model, checkpoint, config):
    """
    对单张图像进行预测。
    """
    class_names = checkpoint.get('class_names')  # 使用 .get() 更安全
    if not class_names:
        print("警告: Checkpoint 中未找到 'class_names'。")
    # 图像预处理
    transform = transforms.Compose([
            PadToSquare(config.IMAGE_SIZE),
            transforms.Grayscale(num_output_channels=3),
            transforms.ToTensor(),
            transforms.Normalize(mean=config.IMAGENET_MEAN, std=config.IMAGENET_STD)
    ])

    image = Image.open(image_path).convert("RGB")
    image_tensor = transform(image).unsqueeze(0).to(config.device)

    # 预测
    model.eval()
    with torch.no_grad():
        outputs = model(image_tensor)
        _, predicted_class = torch.max(outputs, 1)
    if class_names:
        return class_names[predicted_class.item()]
    return predicted_class.item()


def load_trained_model(checkpoint_path, config, freeze=True):
    # 1. 初始化一个未经预训练的 ResNet-50 骨架
    #    如果你想加载 PyTorch 官方的 ImageNet 预训练权重作为基础，
    #    使用 weights=models.ResNet50_Weights.DEFAULT
    model = models.resnet50(weights=None)

    # 2. 修改全连接层以匹配你保存的模型结构
    num_ftrs = model.fc.in_features
    model.fc = nn.Sequential(
        nn.Linear(num_ftrs, 512),
        nn.ReLU(),
        nn.Dropout(0.5),
        nn.Linear(512, config.NUM_CLASSES)
    )

    # 3. 加载你的权重
    #    strict=False 可以在某些层不匹配时（比如你这次不想加载FC层）避免报错
    #    但在这里，我们期望结构是完全匹配的，所以默认 strict=True 即可

    print(f"正在从 {checkpoint_path} 加载模型权重...")
    checkpoint = torch.load(checkpoint_path, map_location=config.device, weights_only=True)
    model.load_state_dict(checkpoint['model_state_dict'])

    # 4. 根据需要冻结层
    if freeze:
        # 冻结所有层
        for param in model.parameters():
            param.requires_grad = False
        # 然后只解冻新加的全连接层
        # 注意：这里需要迭代 model.fc 的参数
        for param in model.fc.parameters():
            param.requires_grad = True

    # 5. 将模型移动到指定设备
    model.to(config.device)

    return model,checkpoint


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="predict image lps")
    parser.add_argument('-f', help='input img path')
    parser.add_argument('-t', default='cor', help='if is img it need and in cor or sag')
    args = parser.parse_args()
    config = Config()
    # 假设你已经有训练好的模型权重
    checkpoint_file = os.path.join(config.CHECKPOINT_DIR, f"{args.t}_best_mode.pth")

    if not os.path.exists(checkpoint_file):
        print("错误：未找到模型权重文件。请先运行训练脚本。")
    else:
        # 加载模型
        model, checkpoint = load_trained_model(checkpoint_file, config)
        if os.path.exists(args.f):
            predicted_class = predict_image(args.f, model, checkpoint, config)
            print(f"图像 {args.f} 的预测类别是: {predicted_class}")
        else:
            print(f"错误：测试图像文件 {args.f} 不存在。")