#!/usr/bin/env python3
"""
医学影像MR序列分类训练脚本 - 支持动态序列发现
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import time
import copy

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.config import Config
from src.data_loader import get_medical_data_loaders
from examples.complete_medical_workflow import discover_sequence_types
from torchvision import models


def create_model_with_dynamic_classes(num_classes, model_name='resnet18', pretrained=True):
    """
    创建支持动态类别数的模型
    
    Args:
        num_classes (int): 动态发现的类别数
        model_name (str): 模型名称
        pretrained (bool): 是否使用预训练权重
    """
    print(f"创建模型: {model_name}, 类别数: {num_classes}")
    
    if model_name == 'resnet18':
        model = models.resnet18(pretrained=pretrained)
        model.fc = nn.Linear(model.fc.in_features, num_classes)
    elif model_name == 'resnet50':
        model = models.resnet50(pretrained=pretrained)
        model.fc = nn.Linear(model.fc.in_features, num_classes)
    elif model_name == 'efficientnet_b0':
        model = models.efficientnet_b0(pretrained=pretrained)
        model.classifier[1] = nn.Linear(model.classifier[1].in_features, num_classes)
    else:
        raise ValueError(f"不支持的模型: {model_name}")
    
    return model


def train_medical_model_dynamic(config, slice_mode='middle', axis='z', model_name='resnet18'):
    """
    训练医学影像分类模型，支持动态序列发现
    
    Args:
        config: 配置对象
        slice_mode (str): 切片模式
        axis (str): 切片轴向
        model_name (str): 模型名称
    """
    print("=" * 60)
    print("医学影像MR序列分类训练 - 动态序列发现")
    print("=" * 60)
    
    # 1. 动态发现序列类型
    train_dir = os.path.join(config.MEDICAL_DATA_DIR, 'train')
    if not os.path.exists(train_dir):
        print(f"❌ 训练目录不存在: {train_dir}")
        return None
    
    print("🔍 动态发现序列类型...")
    discovered_sequences = discover_sequence_types(train_dir)
    
    if not discovered_sequences:
        print("❌ 未发现任何序列类型，使用默认配置")
        discovered_sequences = config.FALLBACK_SEQUENCES
    
    print(f"✅ 发现序列类型: {discovered_sequences}")
    num_classes = len(discovered_sequences)
    
    # 2. 创建数据加载器
    print(f"\n📊 创建数据加载器...")
    print(f"  切片模式: {slice_mode}")
    if slice_mode == 'multi':
        print(f"  切片轴向: {axis}")
    
    try:
        data_loaders, class_names, dataset_sizes = get_medical_data_loaders(
            config, slice_mode=slice_mode, axis=axis
        )
        
        if not data_loaders:
            print("❌ 无法创建数据加载器")
            return None
        
        # 验证发现的序列与实际类别是否一致
        if set(discovered_sequences) != set(class_names):
            print(f"⚠️  警告: 发现的序列 {discovered_sequences} 与实际类别 {class_names} 不完全一致")
            # 使用实际的类别数
            num_classes = len(class_names)
            print(f"  使用实际类别数: {num_classes}")
        
    except Exception as e:
        print(f"❌ 创建数据加载器失败: {e}")
        return None
    
    # 3. 创建模型
    print(f"\n🤖 创建模型...")
    try:
        model = create_model_with_dynamic_classes(
            num_classes=num_classes,
            model_name=model_name,
            pretrained=True
        )
        model = model.to(config.device)
        print(f"✅ 模型已创建并移至设备: {config.device}")
    except Exception as e:
        print(f"❌ 创建模型失败: {e}")
        return None
    
    # 4. 定义损失函数和优化器
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=config.LEARNING_RATE)
    
    # 5. 训练模型
    print(f"\n🚀 开始训练...")
    print(f"  训练轮数: {config.NUM_EPOCHS}")
    print(f"  学习率: {config.LEARNING_RATE}")
    print(f"  批次大小: {config.BATCH_SIZE}")
    
    best_model_wts = copy.deepcopy(model.state_dict())
    best_acc = 0.0
    
    for epoch in range(config.NUM_EPOCHS):
        print(f'\nEpoch {epoch+1}/{config.NUM_EPOCHS}')
        print('-' * 20)
        
        # 每个epoch都有训练和验证阶段
        for phase in ['train', 'val']:
            if phase not in data_loaders:
                continue
                
            if phase == 'train':
                model.train()
            else:
                model.eval()
            
            running_loss = 0.0
            running_corrects = 0
            
            # 遍历数据
            for inputs, labels in data_loaders[phase]:
                inputs = inputs.to(config.device)
                labels = labels.to(config.device)
                
                # 清零参数梯度
                optimizer.zero_grad()
                
                # 前向传播
                with torch.set_grad_enabled(phase == 'train'):
                    outputs = model(inputs)
                    _, preds = torch.max(outputs, 1)
                    loss = criterion(outputs, labels)
                    
                    # 反向传播和优化（仅在训练阶段）
                    if phase == 'train':
                        loss.backward()
                        optimizer.step()
                
                # 统计
                running_loss += loss.item() * inputs.size(0)
                running_corrects += torch.sum(preds == labels.data)
            
            epoch_loss = running_loss / dataset_sizes[phase]
            epoch_acc = running_corrects.double() / dataset_sizes[phase]
            
            print(f'{phase} Loss: {epoch_loss:.4f} Acc: {epoch_acc:.4f}')
            
            # 深拷贝最佳模型
            if phase == 'val' and epoch_acc > best_acc:
                best_acc = epoch_acc
                best_model_wts = copy.deepcopy(model.state_dict())
    
    print(f'\n✅ 训练完成！最佳验证准确率: {best_acc:.4f}')
    
    # 加载最佳模型权重
    model.load_state_dict(best_model_wts)
    
    # 6. 保存模型
    model_save_path = os.path.join(
        config.CHECKPOINT_DIR, 
        f'medical_model_{model_name}_{slice_mode}_{axis}_{len(class_names)}classes.pth'
    )
    
    torch.save({
        'model_state_dict': model.state_dict(),
        'class_names': class_names,
        'discovered_sequences': discovered_sequences,
        'num_classes': num_classes,
        'slice_mode': slice_mode,
        'axis': axis,
        'model_name': model_name,
        'best_accuracy': best_acc.item()
    }, model_save_path)
    
    print(f"💾 模型已保存: {model_save_path}")
    
    return model


def main():
    """主函数"""
    config = Config()
    
    # 训练不同配置的模型
    configurations = [
        ('middle', 'z', 'resnet18'),
        ('multi', 'z', 'resnet18'),
    ]
    
    for slice_mode, axis, model_name in configurations:
        print(f"\n{'='*80}")
        print(f"训练配置: {slice_mode}_{axis}_{model_name}")
        print(f"{'='*80}")
        
        model = train_medical_model_dynamic(
            config,
            slice_mode=slice_mode,
            axis=axis,
            model_name=model_name
        )
        
        if model is not None:
            print(f"✅ 配置 {slice_mode}_{axis}_{model_name} 训练成功")
        else:
            print(f"❌ 配置 {slice_mode}_{axis}_{model_name} 训练失败")


if __name__ == "__main__":
    main()
