import os
import torch

import SimpleIT<PERSON> as sitk
from PIL import Image
import numpy as np
import hashlib

def get_md5_hash(file_path):
    """
    Calculates the MD5 hash of a file.

    Args:
        file_path (str): The path to the input file.

    Returns:
        str: The MD5 hash of the file as a hexadecimal string,
             or an error message if the file cannot be read.
    """
    # Initialize the MD5 hash object
    md5_hash = hashlib.md5()

    try:
        # Open the file in binary read mode to ensure consistent hashing across platforms
        with open(file_path, "rb") as f:
            # Read the file in chunks to handle large files efficiently
            for chunk in iter(lambda: f.read(4096), b""):
                # Update the hash object with each chunk
                md5_hash.update(chunk)
    except FileNotFoundError:
        return f"Error: The file '{file_path}' was not found."
    except IOError:
        return f"Error: Could not read the file '{file_path}'."

    # Return the hexadecimal representation of the hash
    return md5_hash.hexdigest()


def image2d_to_obj(arr, resize_rect=None):
    rgb_image = Image.fromarray(arr)
    if resize_rect:
        rgb_image = rgb_image.resize(resize_rect, Image.LANCZOS)
    return rgb_image


def ct_arr_to_gray(arr, window_width=400, window_center=40):
    return np.uint8(np.clip(255 * ((arr - window_center) / (window_width) + 1 / 2), 0, 255))


def cut_nii_to_image_obj(nii_path):
    """原有的nii切片函数，保持向后兼容"""
    image = sitk.ReadImage(nii_path)
    arr = sitk.GetArrayFromImage(image)
    depth, height, width = arr.shape
    spacing_width, spacing_height, spacing_depth = image.GetSpacing()
    arr = arr[::-1, :, :]
    arr1 = arr[:, int(height / 2), :]
    arr2 = arr[:, :, int(width / 2)]

    resize_rect = (width, int(depth * spacing_depth / spacing_width))
    cor_img_obj = image2d_to_obj(ct_arr_to_gray(arr1), resize_rect=resize_rect)

    resize_rect = (height, int(depth * spacing_depth / spacing_height))
    sag_img_obj = image2d_to_obj(ct_arr_to_gray(arr2), resize_rect=resize_rect)
    return cor_img_obj, sag_img_obj


def extract_nii_slices(nii_path, slice_mode='middle', axis='z'):
    """
    从nii.gz文件提取切片的通用函数

    Args:
        nii_path (str): nii.gz文件路径
        slice_mode (str): 切片模式，'middle' 或 'multi'
        axis (str): 当slice_mode='multi'时指定轴向
    Returns:
        PIL Image: 如果stack_channels=True，返回3通道RGB图像
    """
    # 读取医学影像
    image = sitk.ReadImage(nii_path)
    arr = sitk.GetArrayFromImage(image)

    if slice_mode == 'middle':
        return _extract_middle_slices_stacked(arr)
    elif slice_mode == 'multi':
        return _extract_multi_slices_stacked(arr, axis)
    else:
        raise ValueError(f"不支持的切片模式: {slice_mode}")
       

def _extract_middle_slices_stacked(arr):
    """提取xyz面的中间切片并堆叠成3通道RGB图像"""
    depth, height, width = arr.shape

    # Z轴中间切片 (轴向面)
    z_middle = depth // 2
    axial_slice = arr[z_middle, :, :]

    # Y轴中间切片 (冠状面)
    y_middle = height // 2
    coronal_slice = arr[:, y_middle, :]

    # X轴中间切片 (矢状面)
    x_middle = width // 2
    sagittal_slice = arr[:, :, x_middle]

    # 将三个切片堆叠成3通道图像
    return _stack_slices_to_rgb_util(axial_slice, coronal_slice, sagittal_slice)


def _extract_multi_slices_stacked(arr, axis):
    """提取指定轴的25%、50%、75%位置切片并堆叠成3通道RGB图像"""
    depth, height, width = arr.shape

    if axis == 'z':
        positions = [int(depth * 0.25), int(depth * 0.5), int(depth * 0.75)]
        slice1 = arr[min(positions[0], depth - 1), :, :]
        slice2 = arr[min(positions[1], depth - 1), :, :]
        slice3 = arr[min(positions[2], depth - 1), :, :]

    elif axis == 'y':
        positions = [int(height * 0.25), int(height * 0.5), int(height * 0.75)]
        slice1 = arr[:, min(positions[0], height - 1), :]
        slice2 = arr[:, min(positions[1], height - 1), :]
        slice3 = arr[:, min(positions[2], height - 1), :]

    elif axis == 'x':
        positions = [int(width * 0.25), int(width * 0.5), int(width * 0.75)]
        slice1 = arr[:, :, min(positions[0], width - 1)]
        slice2 = arr[:, :, min(positions[1], width - 1)]
        slice3 = arr[:, :, min(positions[2], width - 1)]
    else:
        raise ValueError(f"不支持的轴向: {axis}")

    # 将三个切片堆叠成3通道图像
    return _stack_slices_to_rgb_util(slice1, slice2, slice3)


def _normalize_slice_util(arr):
    """标准化单个切片到0-255范围"""
    arr_min, arr_max = arr.min(), arr.max()
    if arr_max > arr_min:
        return ((arr - arr_min) / (arr_max - arr_min) * 255).astype(np.uint8)
    else:
        return np.zeros_like(arr, dtype=np.uint8)


def _stack_slices_to_rgb_util(slice1, slice2, slice3):
    """将三个切片堆叠成RGB图像"""
    # 确保所有切片具有相同的尺寸
    target_shape = slice1.shape

    # 如果切片尺寸不同，需要调整到相同尺寸
    if slice2.shape != target_shape:
        slice2 = _resize_slice_util(slice2, target_shape)
    if slice3.shape != target_shape:
        slice3 = _resize_slice_util(slice3, target_shape)

    # 标准化每个切片
    slice1_norm = _normalize_slice_util(slice1)
    slice2_norm = _normalize_slice_util(slice2)
    slice3_norm = _normalize_slice_util(slice3)

    # 堆叠成RGB图像
    rgb_array = np.stack([slice1_norm, slice2_norm, slice3_norm], axis=-1)

    # 转换为PIL图像
    return Image.fromarray(rgb_array, mode='RGB')


def _resize_slice_util(slice_arr, target_shape):
    """调整切片尺寸"""
    try:
        from scipy import ndimage
        zoom_factors = [target_shape[i] / slice_arr.shape[i] for i in range(len(target_shape))]
        return ndimage.zoom(slice_arr, zoom_factors, order=1)
    except ImportError:
        # 如果没有scipy，使用简单的重复/截断方法
        return np.resize(slice_arr, target_shape)


def get_nii_info(nii_path):
    """
    获取nii.gz文件的基本信息

    Args:
        nii_path (str): nii.gz文件路径

    Returns:
        dict: 包含文件信息的字典
    """
    try:
        image = sitk.ReadImage(nii_path)
        arr = sitk.GetArrayFromImage(image)

        info = {
            'file_path': nii_path,
            'shape': arr.shape,
            'spacing': image.GetSpacing(),
            'origin': image.GetOrigin(),
            'direction': image.GetDirection(),
            'pixel_type': image.GetPixelIDTypeAsString(),
            'min_value': float(arr.min()),
            'max_value': float(arr.max()),
            'mean_value': float(arr.mean()),
            'std_value': float(arr.std())
        }

        return info

    except Exception as e:
        return {'error': str(e), 'file_path': nii_path}



def save_checkpoint(model, optimizer, epoch, accuracy, class_names, path, model_name):
    """
    保存模型检查点。

    Args:
        model (torch.nn.Module): 要保存的模型。
        optimizer (torch.optim.Optimizer): 优化器状态。
        epoch (int): 当前训练的周期数。
        accuracy (float): 模型在验证集上的准确率。
        class_names (list): 类别列表
        path (str): 检查点保存的目录。
        model_name (str): model_name
    """
    if not os.path.exists(path):
        os.makedirs(path)

    # 确保文件名是唯一的，并包含关键信息
    checkpoint_path = os.path.join(path, model_name)

    print(f"正在保存模型检查点到 {checkpoint_path} (准确率: {accuracy:.4f})")
    torch.save({
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'accuracy': accuracy,
        'class_names': class_names
    }, checkpoint_path)


# 自定义一个Transform来实现“保持纵横比缩放+填充”
class PadToSquare:
    def __init__(self, size, fill=0):
        self.size = size
        self.fill = fill

    def __call__(self, img):
        # img: a PIL Image
        w, h = img.size
        # 计算缩放后的尺寸
        if w > h:
            new_w = self.size
            new_h = int(h * self.size / w)
        else:
            new_h = self.size
            new_w = int(w * self.size / h)

        resized_img = img.resize((new_w, new_h), Image.BICUBIC)

        # 创建一个正方形的背景
        new_img = Image.new("L", (self.size, self.size), self.fill)  # "L" for grayscale
        # 将缩放后的图片粘贴到中心
        paste_x = (self.size - new_w) // 2
        paste_y = (self.size - new_h) // 2
        new_img.paste(resized_img, (paste_x, paste_y))

        return new_img
