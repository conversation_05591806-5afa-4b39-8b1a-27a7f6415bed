import torch
import os
from config.config import Config
from src.predict_medical import MedicalImagePredictor
import argparse
from multiprocessing import Pool
from tqdm import tqdm
import json
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

# 设置环境变量（在导入torch之前）
os.environ['OMP_NUM_THREADS'] = '1'
os.environ['MKL_NUM_THREADS'] = '1'
os.environ['NUMEXPR_NUM_THREADS'] = '1'

class MRSequencePredictor:
    def __init__(self, model_path=None, slice_mode='middle', axis='z'):
        """
        初始化MR序列分类预测器
        Args:
            model_path (str): 训练好的模型路径
            slice_mode (str): 切片模式，'middle' 或 'multi'
            axis (str): 当slice_mode='multi'时指定轴向
        """
        self.config = Config()
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.slice_mode = slice_mode
        self.axis = axis
        self.predictor = None

        # 如果没有指定模型路径，尝试找到最新的模型
        if model_path is None:
            model_path = self._find_latest_model()

        self.model_path = model_path

    def _find_latest_model(self):
        """查找最新的模型文件"""
        checkpoint_dir = self.config.CHECKPOINT_DIR
        if not os.path.exists(checkpoint_dir):
            raise FileNotFoundError(f"检查点目录不存在: {checkpoint_dir}")

        # 查找医学影像模型文件
        model_patterns = [
            f"resnet50_{self.slice_mode}_{self.axis}_best.pth",
            f"resnet18_{self.slice_mode}_{self.axis}_best.pth",
            "resnet50_middle_z_best.pth",  # 默认模型
            "resnet18_middle_z_best.pth"
        ]

        for pattern in model_patterns:
            model_path = os.path.join(checkpoint_dir, pattern)
            if os.path.exists(model_path):
                logger.info(f"找到模型文件: {model_path}")
                return model_path

        # 如果没找到特定模式的模型，查找任何.pth文件
        pth_files = [f for f in os.listdir(checkpoint_dir) if f.endswith('.pth')]
        if pth_files:
            latest_model = os.path.join(checkpoint_dir, pth_files[0])
            logger.warning(f"未找到匹配的模型，使用: {latest_model}")
            return latest_model

        raise FileNotFoundError(f"在 {checkpoint_dir} 中未找到任何模型文件")

    def load_model(self):
        """加载模型"""
        if self.predictor is None:
            logger.info(f"加载MR序列分类模型: {self.model_path}")
            try:
                self.predictor = MedicalImagePredictor(
                    model_path=self.model_path,
                    slice_mode=self.slice_mode,
                    axis=self.axis,
                    device=self.device
                )
                logger.info(f"模型加载成功，设备: {self.device}")
            except Exception as e:
                logger.error(f"模型加载失败: {e}")
                raise

    def predict_nii(self, nii_path):
        """
        预测nii.gz文件的MR序列类型

        Args:
            nii_path (str): nii.gz文件路径

        Returns:
            dict: 预测结果，包含类别、置信度等信息
        """
        self.load_model()

        try:
            # 使用医学影像预测器进行预测
            result = self.predictor.predict(nii_path, return_probabilities=True)

            logger.info(f"预测结果 - 文件: {os.path.basename(nii_path)}")
            logger.info(f"预测序列: {result['predicted_class']}")
            logger.info(f"置信度: {result['confidence']:.4f}")

            return result

        except Exception as e:
            logger.error(f"预测文件 {nii_path} 时出错: {e}")
            return {
                'file_path': nii_path,
                'error': str(e),
                'predicted_class': 'unknown',
                'confidence': 0.0
            }

    def predict_nii_simple(self, nii_path):
        """
        简化版预测，只返回类别索引（保持向后兼容）

        Args:
            nii_path (str): nii.gz文件路径

        Returns:
            int: 预测的类别索引
        """
        result = self.predict_nii(nii_path)

        if 'error' in result:
            return -1  # 错误情况返回-1

        # 将类别名称映射为索引（根据您的需要调整）
        class_to_idx = {
            't1': 0,
            't2': 1,
            'flair': 2,
            'dwi': 3
        }

        predicted_class = result.get('predicted_class', 'unknown')
        return class_to_idx.get(predicted_class, -1)

predictor = None

def init_worker(model_path=None, slice_mode='middle', axis='z'):
    """初始化工作进程"""
    global predictor
    # 严格限制线程数
    torch.set_num_threads(1)
    os.environ['OMP_NUM_THREADS'] = '1'
    os.environ['MKL_NUM_THREADS'] = '1'
    os.environ['NUMEXPR_NUM_THREADS'] = '1'
    predictor = MRSequencePredictor(
        model_path=model_path,
        slice_mode=slice_mode,
        axis=axis
    )
    predictor.load_model()

def predict_nii_worker(nii_path: str):
    """工作进程预测函数"""
    global predictor
    try:
        # 使用简化版预测保持向后兼容
        res = predictor.predict_nii_simple(nii_path)
    except Exception as e:
        logger.error(f'{nii_path} {e}')
        return -1  # 错误情况返回-1
    return res


def predict_nii_from_json(json_file_path, model_path=None, slice_mode='middle', axis='z'):
    """从JSON文件批量预测nii文件"""
    with open(json_file_path, 'r') as f:
        hash_list = json.load(f)

    base_dir = '/data01/arspretraindata/rawfiles'
    nii_list = [os.path.join(base_dir, hash[:2], hash[2:5], hash, 'image.nii.gz')
                for hash in hash_list]
    print(f"开始处理 {len(nii_list)} 个文件")
    print(f"使用模型: {model_path or '自动检测'}")
    print(f"切片模式: {slice_mode}, 轴向: {axis}")

    # 使用进程池进行多进程处理
    with Pool(processes=6, initializer=init_worker,
              initargs=(model_path, slice_mode, axis)) as pool:
        # 使用tqdm显示进度条
        results = list(tqdm(
            pool.imap(predict_nii_worker, nii_list),
            total=len(nii_list),
            desc="MR序列预测进度",
            unit="个"
        ))

    print(f"预测完成，结果: {results}")

    # 保存结果
    output_file = f'mr_sequence_result_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
    with open(output_file, 'w') as f:
        json.dump(list(zip(hash_list, results)), f, ensure_ascii=False, indent=2)

    print(f'预测成功，结果保存到: {output_file}')

def predict_nii_from_list(nii_list, model_path=None, slice_mode='middle', axis='z'):
    """从文件列表批量预测"""
    print(f"开始处理 {len(nii_list)} 个文件")

    # 创建预测器
    predictor = MRSequencePredictor(
        model_path=model_path,
        slice_mode=slice_mode,
        axis=axis
    )

    results = []
    for nii_path in tqdm(nii_list, desc="预测进度", unit="个"):
        try:
            result = predictor.predict_nii(nii_path)
            results.append(result)
            print(f"✓ {os.path.basename(nii_path)}: {result['predicted_class']} "
                  f"(置信度: {result['confidence']:.3f})")
        except Exception as e:
            logger.error(f"预测 {nii_path} 失败: {e}")
            results.append({
                'file_path': nii_path,
                'error': str(e)
            })

    return results



if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="MR序列分类预测")
    parser.add_argument('-f', '--file', required=True, help='输入nii文件路径或JSON文件路径')
    parser.add_argument('--model', help='模型文件路径（可选，默认自动检测）')
    parser.add_argument('--slice_mode', default='middle', choices=['middle', 'multi'],
                       help='切片模式（默认: middle）')
    parser.add_argument('--axis', default='z', choices=['x', 'y', 'z'],
                       help='multi模式下的轴向（默认: z）')
    parser.add_argument('--verbose', action='store_true', help='显示详细信息')

    args = parser.parse_args()

    # 设置日志级别
    if args.verbose:
        logging.basicConfig(level=logging.INFO)

    try:
        if args.file.endswith('.json'):
            # 批量预测
            predict_nii_from_json(
                args.file,
                model_path=args.model,
                slice_mode=args.slice_mode,
                axis=args.axis
            )
        else:
            # 单文件预测
            predictor = MRSequencePredictor(
                model_path=args.model,
                slice_mode=args.slice_mode,
                axis=args.axis
            )

            result = predictor.predict_nii(args.file)

            if 'error' in result:
                print(f"❌ 预测失败: {result['error']}")
            else:
                print(f"📁 文件: {os.path.basename(args.file)}")
                print(f"🔬 预测序列: {result['predicted_class']}")
                print(f"📊 置信度: {result['confidence']:.4f}")
                print(f"⚙️  切片模式: {result['slice_mode']}")
                if result.get('axis'):
                    print(f"📐 轴向: {result['axis']}")

                if 'probabilities' in result:
                    print("\n📈 概率分布:")
                    for seq_type, prob in result['probabilities'].items():
                        print(f"   {seq_type}: {prob:.4f}")

    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        print(f"❌ 执行失败: {e}")
        exit(1)
