import json

import torch
import torch.nn as nn
import torch.optim as optim
from torchvision import models
from torch.utils.tensorboard import SummaryWriter
import os
from config.config import Config
from src.data_loader import get_data_loaders
from src.utils import save_checkpoint, get_md5_hash
from torch.optim import lr_scheduler
import time
import copy
from datetime import datetime

from sklearn.metrics import classification_report, confusion_matrix


def build_model(num_classes, pretrained_path=None, freeze=True):
    """
    构建一个ResNet-50模型，并从本地文件加载预训练权重。

    参数:
    - num_classes (int): 输出分类的数量。
    - pretrained_path (str): 本地预训练权重文件 (.pth) 的路径。
    - freeze (bool): 是否冻结卷积层的权重。
    """
    # 1. 创建一个不带预训练权重的模型实例
    #    设置 weights=None 来避免自动下载
    model = models.resnet50(weights=None)

    # 2. 从本地文件加载预训练权重
    #    确保 PyTorch 版本 >= 1.13，低版本可能没有 weights 参数
    #    如果 pretrained_path 不为 None，则加载权重
    if pretrained_path:
        print(f"Loading pretrained weights from: {pretrained_path}")
        # 使用 torch.load 加载权重文件
        state_dict = torch.load(pretrained_path)
        # 将权重加载到模型中
        model.load_state_dict(state_dict)

    # 3. (可选) 冻结模型参数
    if freeze:
        for param in model.parameters():
            param.requires_grad = False

    # 4. 修改全连接层以适应新的分类任务
    num_ftrs = model.fc.in_features
    model.fc = nn.Sequential(
        nn.Linear(num_ftrs, 512),
        nn.ReLU(),
        nn.Dropout(0.5),
        nn.Linear(512, num_classes)
    )

    # 将新添加的全连接层的参数设置为需要梯度更新
    # 这一步很重要，即使之前冻结了所有层，也要确保新层是可训练的
    for param in model.fc.parameters():
        param.requires_grad = True

    return model

def train_model(config, model_name='best_model.pth'):
    # 初始化 TensorBoard
    writer = SummaryWriter(os.path.join(config.LOG_DIR, config.EXP_NAME))

    # 获取数据加载器
    data_loaders, class_names, dataset_sizes = get_data_loaders(config)
    num_classes = len(class_names)
    model = build_model(num_classes, config.LOCAL_TRAIN_MODEL_DIR)

    model = model.to(config.device)

    # 定义损失函数和优化器
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=config.LEARNING_RATE)

    # （可选）学习率调度器：例如，每7个epoch将学习率乘以0.1
    exp_lr_scheduler = lr_scheduler.StepLR(optimizer, step_size=7, gamma=0.1)

    since = time.time()

    best_model_wts = copy.deepcopy(model.state_dict())
    best_acc = 0.0

    history = {'train_loss': [], 'train_acc': [], 'val_loss': [], 'val_acc': []}

    for epoch in range(config.NUM_EPOCHS):
        print(f'Epoch {epoch}/{config.NUM_EPOCHS - 1}')
        print('-' * 10)

        # 每个epoch都有一个训练和验证阶段
        for phase in ['train', 'val']:
            if phase == 'train':
                model.train()  # 设置模型为训练模式
            else:
                model.eval()  # 设置模型为评估模式

            running_loss = 0.0
            running_corrects = 0

            # 迭代数据
            for inputs, labels in data_loaders[phase]:
                inputs = inputs.to(config.device)
                labels = labels.to(config.device)

                # 梯度清零
                optimizer.zero_grad()

                # 前向传播
                # 只在训练阶段开启梯度计算
                with torch.set_grad_enabled(phase == 'train'):
                    outputs = model(inputs)
                    _, preds = torch.max(outputs, 1)
                    loss = criterion(outputs, labels)

                    # 只在训练阶段进行反向传播和优化
                    if phase == 'train':
                        loss.backward()
                        optimizer.step()

                # 统计损失和准确率
                running_loss += loss.item() * inputs.size(0)
                running_corrects += torch.sum(preds == labels.data)

            if phase == 'train':
                exp_lr_scheduler.step()

            epoch_loss = running_loss / dataset_sizes[phase]
            epoch_acc = running_corrects.double() / dataset_sizes[phase]

            print(f'{phase} Loss: {epoch_loss:.4f} Acc: {epoch_acc:.4f}')

            if phase == 'train':
                history['train_loss'].append(epoch_loss)
                history['train_acc'].append(epoch_acc.item())
            else:
                history['val_loss'].append(epoch_loss)
                history['val_acc'].append(epoch_acc.item())

            # 保存最佳模型
            if phase == 'val' and epoch_acc > best_acc:
                best_acc = epoch_acc
                best_model_wts = copy.deepcopy(model.state_dict())
                # 保存模型权重
                # torch.save(model.state_dict(), config.CHECKPOINT_DIR)
                save_checkpoint(model, optimizer, epoch,best_acc, class_names, config.CHECKPOINT_DIR, model_name)
                print("==> Best model saved!")

    time_elapsed = time.time() - since
    print(f'Training complete in {time_elapsed // 60:.0f}m {time_elapsed % 60:.0f}s')
    print(f'Best val Acc: {best_acc:4f}')

    # 加载最佳模型权重
    model.load_state_dict(best_model_wts)

    writer.close()
    print("训练完成！")
    return model, history

def evaluate_model(model, dataloader, device):
    """
    在给定的数据集上评估模型。

    返回:
        真实标签和预测标签
    """
    model.eval()  # 设置为评估模式
    all_labels = []
    all_preds = []

    with torch.no_grad():  # 在评估阶段不计算梯度
        for inputs, labels in dataloader:
            inputs = inputs.to(device)
            labels = labels.to(device)

            outputs = model(inputs)
            _, preds = torch.max(outputs, 1)

            all_labels.extend(labels.cpu().numpy())
            all_preds.extend(preds.cpu().numpy())

    return all_labels, all_preds


def save_version_info(config):
    version_info   = {
        "version": f"{datetime.now().strftime('%Y%m%d%H%M%S')}",
        "cor_mode_md5": get_md5_hash(os.path.join(config.CHECKPOINT_DIR, 'cor_best_mode.pth')),
        "seg_mode_md5": get_md5_hash(os.path.join(config.CHECKPOINT_DIR, 'sag_best_mode.pth'))
    }
    with open(f"{os.path.join(config.CHECKPOINT_DIR,'version.json')}", 'w', encoding='utf-8') as f:
        json.dump(version_info, f, indent=2, ensure_ascii=False)


if __name__ == '__main__':
    # 从配置文件中加载参数
    print('\n Start train cor model')
    config = Config()
    data_loaders, class_names, dataset_sizes = get_data_loaders(config)
    model_ft, history = train_model(config, model_name='cor_best_mode.pth')
    # 在测试集上进行评估
    y_true, y_pred = evaluate_model(model_ft, data_loaders['test'], config.device)
    # --- 打印分类报告 ---
    print("\n Cor Model Classification Report:")
    print(classification_report(y_true, y_pred, target_names=class_names))

    print('\n Start train sag model')
    config.DATA_DIR = os.path.join(config.BASE_DIR,  "data", 'processed', 'sag_dataset')
    data_loaders, class_names, dataset_sizes = get_data_loaders(config)
    model_ft, history = train_model(config, model_name='sag_best_mode.pth')
    # 在测试集上进行评估
    y_true, y_pred = evaluate_model(model_ft, data_loaders['test'], config.device)
    # --- 打印分类报告 ---
    print("\n Cor Model Classification Report:")
    print(classification_report(y_true, y_pred, target_names=class_names))
    # 生成版本信息
    save_version_info(config)

