#!/usr/bin/env python3
"""
3通道切片堆叠演示脚本

演示如何将医学影像的三个切面堆叠成3通道RGB图像，
这样可以更好地利用预训练的CNN模型进行MR序列分类。
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import SimpleITK as sitk
from PIL import Image

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.data_loader import MedicalImageDataset
from src.utils import extract_nii_slices
from config.config import Config


def create_demo_nii_file(output_path, shape=(64, 64, 64)):
    """创建一个演示用的nii.gz文件"""
    # 创建一个具有不同区域的3D数组
    arr = np.zeros(shape, dtype=np.float32)
    
    # 添加一些结构来模拟脑部影像
    center_z, center_y, center_x = shape[0]//2, shape[1]//2, shape[2]//2
    
    # 创建一个椭球形结构
    for z in range(shape[0]):
        for y in range(shape[1]):
            for x in range(shape[2]):
                # 椭球方程
                if ((z-center_z)/20)**2 + ((y-center_y)/25)**2 + ((x-center_x)/25)**2 < 1:
                    arr[z, y, x] = 800 + 200 * np.sin(z/5) * np.cos(y/5)
                
                # 添加一些噪声
                arr[z, y, x] += np.random.normal(0, 50)
    
    # 创建SimpleITK图像
    image = sitk.GetImageFromArray(arr)
    image.SetSpacing([1.0, 1.0, 1.0])
    image.SetOrigin([0.0, 0.0, 0.0])
    
    # 保存文件
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    sitk.WriteImage(image, output_path)
    print(f"✓ 创建演示文件: {output_path}")
    
    return output_path


def visualize_slice_stacking(nii_path, slice_mode='middle', axis='z'):
    """可视化切片堆叠过程"""
    print(f"\n可视化切片堆叠: {slice_mode}模式, {axis}轴")
    print("-" * 50)
    
    # 读取原始数据
    image = sitk.ReadImage(nii_path)
    arr = sitk.GetArrayFromImage(image)
    print(f"原始影像尺寸: {arr.shape}")
    
    # 提取单独的切片（不堆叠）
    individual_slices = extract_nii_slices(nii_path, slice_mode, axis, stack_channels=False)
    
    # 提取堆叠的3通道图像
    stacked_image = extract_nii_slices(nii_path, slice_mode, axis, stack_channels=True)
    
    # 创建可视化
    fig, axes = plt.subplots(2, 4, figsize=(16, 8))
    
    # 第一行：显示单独的切片
    for i, slice_img in enumerate(individual_slices[:3]):
        axes[0, i].imshow(slice_img, cmap='gray')
        if slice_mode == 'middle':
            titles = ['轴向面(Z)', '冠状面(Y)', '矢状面(X)']
            axes[0, i].set_title(f'切片 {i+1}: {titles[i]}')
        else:
            axes[0, i].set_title(f'切片 {i+1}: {axis.upper()}轴 {25*(i+1)}%')
        axes[0, i].axis('off')
    
    # 显示堆叠后的RGB图像
    axes[0, 3].imshow(stacked_image)
    axes[0, 3].set_title('3通道堆叠图像')
    axes[0, 3].axis('off')
    
    # 第二行：显示每个通道
    stacked_array = np.array(stacked_image)
    for i in range(3):
        axes[1, i].imshow(stacked_array[:, :, i], cmap='gray')
        axes[1, i].set_title(f'通道 {i+1} (R/G/B)')
        axes[1, i].axis('off')
    
    # 显示RGB合成
    axes[1, 3].imshow(stacked_image)
    axes[1, 3].set_title('RGB合成图像')
    axes[1, 3].axis('off')
    
    plt.suptitle(f'3通道切片堆叠演示 - {slice_mode}模式 {axis}轴')
    plt.tight_layout()
    
    # 保存图像
    save_path = f"slice_stacking_demo_{slice_mode}_{axis}.png"
    plt.savefig(save_path, dpi=150, bbox_inches='tight')
    print(f"✓ 可视化结果保存到: {save_path}")
    plt.close()
    
    return stacked_image


def compare_stacking_modes(nii_path):
    """比较不同堆叠模式的效果"""
    print("\n比较不同堆叠模式")
    print("=" * 50)
    
    modes = [
        ('middle', 'z'),
        ('multi', 'z'),
        ('multi', 'y'),
        ('multi', 'x')
    ]
    
    fig, axes = plt.subplots(2, 4, figsize=(16, 8))
    axes = axes.flatten()
    
    for i, (slice_mode, axis) in enumerate(modes):
        # 提取堆叠图像
        stacked_image = extract_nii_slices(nii_path, slice_mode, axis, stack_channels=True)
        
        # 显示图像
        axes[i].imshow(stacked_image)
        axes[i].set_title(f'{slice_mode}模式 {axis}轴')
        axes[i].axis('off')
        
        # 显示每个通道的直方图
        stacked_array = np.array(stacked_image)
        axes[i+4].hist(stacked_array[:, :, 0].flatten(), bins=50, alpha=0.5, color='red', label='R')
        axes[i+4].hist(stacked_array[:, :, 1].flatten(), bins=50, alpha=0.5, color='green', label='G')
        axes[i+4].hist(stacked_array[:, :, 2].flatten(), bins=50, alpha=0.5, color='blue', label='B')
        axes[i+4].set_title(f'像素值分布')
        axes[i+4].legend()
        axes[i+4].set_xlabel('像素值')
        axes[i+4].set_ylabel('频次')
    
    plt.suptitle('不同堆叠模式比较')
    plt.tight_layout()
    
    save_path = "stacking_modes_comparison.png"
    plt.savefig(save_path, dpi=150, bbox_inches='tight')
    print(f"✓ 比较结果保存到: {save_path}")
    plt.close()


def test_dataset_with_stacking():
    """测试数据集的3通道堆叠功能"""
    print("\n测试数据集3通道堆叠功能")
    print("=" * 50)
    
    # 创建测试数据
    test_dir = "data/demo/train/t1"
    os.makedirs(test_dir, exist_ok=True)
    
    demo_file = os.path.join(test_dir, "demo_t1.nii.gz")
    create_demo_nii_file(demo_file)
    
    # 创建数据集
    dataset = MedicalImageDataset(
        data_dir="data/demo/train",
        slice_mode='middle'
    )
    
    if len(dataset) > 0:
        # 获取一个样本
        image, label = dataset[0]
        print(f"✓ 数据集样本:")
        print(f"  图像类型: {type(image)}")
        print(f"  图像模式: {image.mode}")
        print(f"  图像尺寸: {image.size}")
        print(f"  标签: {label}")
        print(f"  类别名称: {dataset.get_class_names()}")
        
        # 可视化样本
        plt.figure(figsize=(8, 6))
        plt.imshow(image)
        plt.title(f'数据集样本 - 类别: {dataset.get_class_names()[label]}')
        plt.axis('off')
        
        save_path = "dataset_sample_3channel.png"
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        print(f"✓ 数据集样本保存到: {save_path}")
        plt.close()
        
        return True
    else:
        print("✗ 数据集为空")
        return False


def demonstrate_preprocessing_pipeline():
    """演示完整的预处理流程"""
    print("\n演示完整预处理流程")
    print("=" * 50)
    
    from torchvision import transforms
    from src.utils import PadToSquare
    
    # 创建演示文件
    demo_file = "data/demo/preprocessing_demo.nii.gz"
    create_demo_nii_file(demo_file, shape=(80, 90, 70))  # 不规则尺寸
    
    # 提取3通道图像
    stacked_image = extract_nii_slices(demo_file, 'middle', stack_channels=True)
    print(f"原始3通道图像尺寸: {stacked_image.size}")
    
    # 定义预处理流程
    transform = transforms.Compose([
        PadToSquare(224),  # 填充到正方形
        transforms.ToTensor(),  # 转换为张量
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])  # 标准化
    ])
    
    # 应用预处理
    processed_tensor = transform(stacked_image)
    print(f"预处理后张量形状: {processed_tensor.shape}")
    print(f"张量数据类型: {processed_tensor.dtype}")
    print(f"张量值范围: [{processed_tensor.min():.3f}, {processed_tensor.max():.3f}]")
    
    # 可视化预处理前后的对比
    fig, axes = plt.subplots(1, 2, figsize=(12, 5))
    
    # 原始图像
    axes[0].imshow(stacked_image)
    axes[0].set_title(f'原始3通道图像\n尺寸: {stacked_image.size}')
    axes[0].axis('off')
    
    # 预处理后的图像（反标准化用于显示）
    # 反标准化
    mean = torch.tensor([0.485, 0.456, 0.406]).view(3, 1, 1)
    std = torch.tensor([0.229, 0.224, 0.225]).view(3, 1, 1)
    denormalized = processed_tensor * std + mean
    denormalized = torch.clamp(denormalized, 0, 1)
    
    # 转换为numpy用于显示
    display_image = denormalized.permute(1, 2, 0).numpy()
    axes[1].imshow(display_image)
    axes[1].set_title(f'预处理后图像\n形状: {processed_tensor.shape}')
    axes[1].axis('off')
    
    plt.suptitle('预处理流程演示')
    plt.tight_layout()
    
    save_path = "preprocessing_pipeline_demo.png"
    plt.savefig(save_path, dpi=150, bbox_inches='tight')
    print(f"✓ 预处理演示保存到: {save_path}")
    plt.close()


def main():
    """主函数"""
    print("=" * 60)
    print("3通道切片堆叠演示")
    print("=" * 60)
    
    # 创建演示文件
    demo_file = "data/demo/demo_brain.nii.gz"
    create_demo_nii_file(demo_file)
    
    # 1. 可视化不同切片模式的堆叠效果
    for slice_mode, axis in [('middle', 'z'), ('multi', 'z'), ('multi', 'y')]:
        visualize_slice_stacking(demo_file, slice_mode, axis)
    
    # 2. 比较不同堆叠模式
    compare_stacking_modes(demo_file)
    
    # 3. 测试数据集功能
    test_dataset_with_stacking()
    
    # 4. 演示完整预处理流程
    demonstrate_preprocessing_pipeline()
    
    print("\n" + "=" * 60)
    print("演示完成！")
    print("=" * 60)
    print("主要特性:")
    print("✓ 3通道切片堆叠 (轴向面 + 冠状面 + 矢状面)")
    print("✓ 支持middle和multi两种切片模式")
    print("✓ 自动尺寸调整和标准化")
    print("✓ 与PyTorch预训练模型兼容")
    print("✓ 完整的预处理流程")
    
    print("\n使用建议:")
    print("- middle模式: 适合大多数情况，提供三个正交面的信息")
    print("- multi模式: 适合需要同一轴向多个位置信息的情况")
    print("- 3通道堆叠: 充分利用预训练CNN模型的特征提取能力")


if __name__ == "__main__":
    import torch  # 添加这个导入
    main()
