#!/usr/bin/env python3
"""
医学影像数据集使用示例

这个脚本演示了如何使用新的MedicalImageDataset类来加载和处理nii.gz格式的医学影像数据。
支持两种切片模式：
1. middle模式：提取xyz面的中间切片
2. multi模式：提取指定轴的25%、50%、75%位置切片
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import matplotlib.pyplot as plt
from config.config import Config
from src.data_loader import get_medical_data_loaders, MedicalImageDataset
from torchvision import transforms

def visualize_slices(dataset, idx=0, save_path=None):
    """可视化数据集中的切片"""
    # 创建一个不做变换的数据集用于可视化
    vis_dataset = MedicalImageDataset(
        data_dir=dataset.data_dir,
        slice_mode=dataset.slice_mode,
        axis=dataset.axis,
        transform=None
    )
    
    # 获取原始切片
    nii_path = vis_dataset.image_paths[idx]
    slices = vis_dataset._extract_slices(nii_path)
    label = vis_dataset.labels[idx]
    class_name = vis_dataset.idx_to_label[label]
    
    print(f"文件: {os.path.basename(nii_path)}")
    print(f"标签: {class_name}")
    print(f"切片模式: {dataset.slice_mode}")
    if dataset.slice_mode == 'multi':
        print(f"轴向: {dataset.axis}")
    
    # 可视化切片
    fig, axes = plt.subplots(1, len(slices), figsize=(15, 5))
    if len(slices) == 1:
        axes = [axes]
    
    for i, slice_img in enumerate(slices):
        axes[i].imshow(slice_img, cmap='gray')
        if dataset.slice_mode == 'middle':
            titles = ['轴向面(Z)', '冠状面(Y)', '矢状面(X)']
            axes[i].set_title(titles[i])
        else:
            axes[i].set_title(f'{dataset.axis.upper()}轴 {25*(i+1)}%位置')
        axes[i].axis('off')
    
    plt.suptitle(f'医学影像切片 - {class_name}')
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        print(f"可视化结果已保存到: {save_path}")
    else:
        plt.show()
    
    plt.close()

def test_dataset_loading():
    """测试数据集加载功能"""
    config = Config()
    
    print("=" * 60)
    print("测试医学影像数据集加载")
    print("=" * 60)
    
    # 测试不同的切片模式
    slice_modes = [
        ('middle', 'z'),  # 中间切片模式
        ('multi', 'z'),   # z轴多切片模式
        ('multi', 'y'),   # y轴多切片模式
        ('multi', 'x'),   # x轴多切片模式
    ]
    
    for slice_mode, axis in slice_modes:
        print(f"\n测试切片模式: {slice_mode}, 轴向: {axis}")
        print("-" * 40)
        
        try:
            # 创建数据加载器
            data_loaders, class_names, dataset_sizes = get_medical_data_loaders(
                config, 
                slice_mode=slice_mode, 
                axis=axis
            )
            
            if 'train' in data_loaders:
                # 测试数据加载
                train_loader = data_loaders['train']
                print(f"成功创建训练数据加载器，批次大小: {train_loader.batch_size}")
                
                # 获取一个批次的数据
                for batch_idx, (images, labels) in enumerate(train_loader):
                    print(f"批次 {batch_idx}: 图像形状 {images.shape}, 标签形状 {labels.shape}")
                    print(f"图像数据类型: {images.dtype}, 值范围: [{images.min():.3f}, {images.max():.3f}]")
                    
                    # 只测试第一个批次
                    break
                
                # 可视化第一个样本
                if len(train_loader.dataset) > 0:
                    print("可视化第一个样本...")
                    visualize_slices(
                        train_loader.dataset, 
                        idx=0,
                        save_path=f"medical_sample_{slice_mode}_{axis}.png"
                    )
            else:
                print("未找到训练数据")
                
        except Exception as e:
            print(f"错误: {e}")

def create_sample_data_structure():
    """创建示例数据目录结构"""
    sample_dir = "data/sample_medical"
    
    # 创建示例目录结构
    sequences = ['t1', 't2', 'flair', 'dwi']
    splits = ['train', 'val', 'test']
    
    print("创建示例数据目录结构...")
    for split in splits:
        for seq in sequences:
            dir_path = os.path.join(sample_dir, split, seq)
            os.makedirs(dir_path, exist_ok=True)
            print(f"创建目录: {dir_path}")
    
    print(f"\n示例数据目录结构已创建在: {sample_dir}")
    print("请将您的nii.gz文件放入相应的目录中：")
    print("- t1序列文件放入 t1/ 目录")
    print("- t2序列文件放入 t2/ 目录")
    print("- flair序列文件放入 flair/ 目录")
    print("- dwi序列文件放入 dwi/ 目录")

if __name__ == "__main__":
    # 创建示例数据目录结构
    create_sample_data_structure()
    
    # 测试数据集加载（如果有数据的话）
    try:
        test_dataset_loading()
    except Exception as e:
        print(f"\n数据集测试失败: {e}")
        print("请确保数据目录中有nii.gz文件")
