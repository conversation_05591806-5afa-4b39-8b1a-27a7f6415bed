#!/usr/bin/env python3
"""
完整的医学影像MR序列分类工作流程示例

这个脚本展示了从数据准备到模型训练的完整流程：
1. 数据集准备和验证
2. 数据加载器创建
3. 模型训练
4. 结果评估
5. 模型保存和加载
"""

import os
import sys
import torch
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import classification_report, confusion_matrix
import seaborn as sns

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.config import Config
from src.data_loader import get_medical_data_loaders, MedicalImageDataset
from src.train_medical import train_medical_model
from src.utils import get_nii_info
from torchvision import models
import torch.nn as nn


def discover_sequence_types(data_dir):
    """动态发现数据目录中的序列类型"""
    sequence_types = set()

    # 遍历数据目录，查找所有可能的序列类型
    for root, dirs, files in os.walk(data_dir):
        # 检查是否有nii文件的目录
        has_nii_files = any(f.endswith('.nii.gz') or f.endswith('.nii') for f in files)

        if has_nii_files:
            # 从路径中提取可能的序列类型
            path_parts = root.split(os.sep)
            for part in path_parts:
                part_lower = part.lower()
                # 检查是否是常见的MR序列类型模式
                if (len(part_lower) >= 2 and
                    (part_lower.startswith(('t1', 't2', 'flair', 'dwi', 'adc', 'b0', 'swi', 'pd')) or
                     part_lower in ['t1', 't2', 'flair', 'dwi', 'adc', 'b0', 'swi', 'pd', 'bold', 'dti'])):
                    sequence_types.add(part_lower)

        # 也检查直接的子目录名称
        for dir_name in dirs:
            dir_lower = dir_name.lower()
            # 检查子目录是否包含nii文件
            subdir_path = os.path.join(root, dir_name)
            subdir_files = os.listdir(subdir_path) if os.path.exists(subdir_path) else []
            has_nii_in_subdir = any(f.endswith('.nii.gz') or f.endswith('.nii') for f in subdir_files)

            if has_nii_in_subdir and (
                len(dir_lower) >= 2 and
                (dir_lower.startswith(('t1', 't2', 'flair', 'dwi', 'adc', 'b0', 'swi', 'pd')) or
                 dir_lower in ['t1', 't2', 'flair', 'dwi', 'adc', 'b0', 'swi', 'pd', 'bold', 'dti'])):
                sequence_types.add(dir_lower)

    return sorted(list(sequence_types))


def analyze_dataset(data_dir):
    """分析数据集的基本信息"""
    print("=" * 60)
    print("数据集分析")
    print("=" * 60)

    # 首先动态发现序列类型
    discovered_sequences = discover_sequence_types(data_dir)
    print(f"发现的序列类型: {discovered_sequences}")

    total_files = 0
    sequence_counts = {}
    file_info_list = []

    # 遍历数据目录
    for root, _, files in os.walk(data_dir):
        for file in files:
            if file.endswith('.nii.gz') or file.endswith('.nii'):
                file_path = os.path.join(root, file)
                total_files += 1

                # 从路径推断序列类型（使用发现的序列类型）
                path_parts = root.split(os.sep)
                sequence = None
                for part in path_parts:
                    if part.lower() in discovered_sequences:
                        sequence = part.lower()
                        break

                # 如果没有从路径找到，尝试从文件名推断
                if not sequence:
                    file_name_lower = file.lower()
                    for seq_type in discovered_sequences:
                        if seq_type in file_name_lower:
                            sequence = seq_type
                            break

                if sequence:
                    sequence_counts[sequence] = sequence_counts.get(sequence, 0) + 1
                else:
                    # 如果无法推断序列类型，标记为unknown
                    sequence_counts['unknown'] = sequence_counts.get('unknown', 0) + 1

                # 获取文件信息
                info = get_nii_info(file_path)
                if 'error' not in info:
                    file_info_list.append(info)

    print(f"总文件数: {total_files}")
    print(f"序列分布: {sequence_counts}")

    if file_info_list:
        # 分析影像尺寸分布
        shapes = [info['shape'] for info in file_info_list]
        unique_shapes = list(set(shapes))
        print(f"影像尺寸分布: {unique_shapes}")

        # 分析像素值范围
        min_values = [info['min_value'] for info in file_info_list]
        max_values = [info['max_value'] for info in file_info_list]
        print(f"像素值范围: [{np.min(min_values):.2f}, {np.max(max_values):.2f}]")

    return sequence_counts, file_info_list, discovered_sequences


def compare_slice_modes(config, sample_size=5):
    """比较不同切片模式的效果"""
    print("\n" + "=" * 60)
    print("切片模式比较")
    print("=" * 60)
    
    modes = [
        ('middle', 'z'),
        ('multi', 'z'),
        ('multi', 'y'),
        ('multi', 'x')
    ]
    
    results = {}
    
    for slice_mode, axis in modes:
        print(f"\n测试模式: {slice_mode}, 轴向: {axis}")
        
        try:
            # 创建数据集
            train_dir = os.path.join(config.MEDICAL_DATA_DIR, 'train')
            if os.path.exists(train_dir):
                dataset = MedicalImageDataset(
                    data_dir=train_dir,
                    slice_mode=slice_mode,
                    axis=axis
                )
                
                if len(dataset) > 0:
                    # 采样几个样本进行分析
                    sample_indices = np.random.choice(
                        len(dataset), 
                        min(sample_size, len(dataset)), 
                        replace=False
                    )
                    
                    mode_key = f"{slice_mode}_{axis}"
                    results[mode_key] = {
                        'dataset_size': len(dataset),
                        'class_names': dataset.get_class_names(),
                        'sample_shapes': []
                    }
                    
                    for idx in sample_indices:
                        image, _ = dataset[idx]  # 不需要使用label
                        results[mode_key]['sample_shapes'].append(image.size)
                    
                    print(f"  数据集大小: {len(dataset)}")
                    print(f"  类别: {dataset.get_class_names()}")
                    print(f"  样本图像尺寸: {results[mode_key]['sample_shapes'][:3]}")
                else:
                    print("  数据集为空")
            else:
                print(f"  训练目录不存在: {train_dir}")
                
        except Exception as e:
            print(f"  错误: {e}")
    
    return results


def train_and_evaluate_models(config):
    """训练和评估不同配置的模型"""
    print("\n" + "=" * 60)
    print("模型训练和评估")
    print("=" * 60)
    
    # 定义要测试的配置
    configurations = [
        ('middle', 'z', 'resnet18'),
        ('multi', 'z', 'resnet18'),
    ]
    
    results = {}
    
    for slice_mode, axis, model_name in configurations:
        config_name = f"{slice_mode}_{axis}_{model_name}"
        print(f"\n训练配置: {config_name}")
        print("-" * 40)
        
        try:
            # 训练模型
            model = train_medical_model(
                config,
                slice_mode=slice_mode,
                axis=axis,
                model_name=model_name
            )
            
            if model is not None:
                # 评估模型
                accuracy = evaluate_model(config, model, slice_mode, axis)
                results[config_name] = {
                    'model': model,
                    'accuracy': accuracy,
                    'slice_mode': slice_mode,
                    'axis': axis
                }
                print(f"✓ 训练完成，准确率: {accuracy:.4f}")
            else:
                print("✗ 训练失败")
                
        except Exception as e:
            print(f"✗ 训练出错: {e}")
    
    return results


def evaluate_model(config, model, slice_mode, axis):
    """评估模型性能"""
    try:
        # 创建测试数据加载器
        data_loaders, class_names, _ = get_medical_data_loaders(
            config, slice_mode=slice_mode, axis=axis
        )
        
        if 'test' not in data_loaders:
            print("未找到测试数据，使用验证数据评估")
            test_loader = data_loaders.get('val')
        else:
            test_loader = data_loaders['test']
        
        if test_loader is None:
            return 0.0
        
        model.eval()
        correct = 0
        total = 0
        all_preds = []
        all_labels = []
        
        with torch.no_grad():
            for images, labels in test_loader:
                images = images.to(config.device)
                labels = labels.to(config.device)
                
                outputs = model(images)
                _, predicted = torch.max(outputs.data, 1)
                
                total += labels.size(0)
                correct += (predicted == labels).sum().item()
                
                all_preds.extend(predicted.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
        
        accuracy = correct / total
        
        # 打印详细的分类报告
        if len(class_names) > 0:
            print("\n分类报告:")
            print(classification_report(
                all_labels, all_preds, 
                target_names=class_names, 
                zero_division=0
            ))
        
        return accuracy
        
    except Exception as e:
        print(f"评估出错: {e}")
        return 0.0


def create_visualization_report(results, save_dir="reports"):
    """创建可视化报告"""
    print("\n" + "=" * 60)
    print("生成可视化报告")
    print("=" * 60)
    
    os.makedirs(save_dir, exist_ok=True)
    
    if not results:
        print("没有结果可以可视化")
        return
    
    # 准确率比较图
    config_names = list(results.keys())
    accuracies = [results[name]['accuracy'] for name in config_names]
    
    plt.figure(figsize=(12, 6))
    bars = plt.bar(config_names, accuracies)
    plt.title('不同配置的模型准确率比较')
    plt.xlabel('配置')
    plt.ylabel('准确率')
    plt.xticks(rotation=45)
    
    # 在柱状图上添加数值标签
    for bar, acc in zip(bars, accuracies):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{acc:.3f}', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'accuracy_comparison.png'), dpi=150)
    plt.close()
    
    print(f"报告已保存到: {save_dir}")


def main():
    """主函数"""
    print("医学影像MR序列分类完整工作流程")
    print("=" * 60)
    
    config = Config()
    
    # 检查数据目录
    if hasattr(config, 'MEDICAL_DATA_DIR'):
        data_dir = config.MEDICAL_DATA_DIR
    else:
        data_dir = os.path.join(config.BASE_DIR, "data", "medical")
    
    if not os.path.exists(data_dir):
        print(f"数据目录不存在: {data_dir}")
        print("请创建数据目录并放入nii.gz文件")
        print("目录结构应为:")
        print("  data/medical/")
        print("    ├── train/")
        print("    │   ├── t1/")
        print("    │   ├── t2/")
        print("    │   ├── flair/")
        print("    │   └── dwi/")
        print("    ├── val/")
        print("    └── test/")
        return
    
    # 1. 分析数据集
    sequence_counts, file_info, discovered_sequences = analyze_dataset(data_dir)

    print(f"\n发现的序列类型: {discovered_sequences}")
    print(f"可以用于训练的序列: {list(sequence_counts.keys())}")

    # 2. 比较切片模式
    slice_results = compare_slice_modes(config)
    print(f"切片模式测试完成，测试了 {len(slice_results)} 种配置")

    # 3. 训练和评估模型（如果有足够的数据）
    if sum(sequence_counts.values()) > 10:  # 至少需要10个文件
        print(f"\n数据量充足（{sum(sequence_counts.values())} 个文件），开始模型训练...")
        model_results = train_and_evaluate_models(config)

        # 4. 生成可视化报告
        create_visualization_report(model_results)
    else:
        print(f"\n数据量不足（仅 {sum(sequence_counts.values())} 个文件），跳过模型训练")
        print("建议每个序列至少有5个样本用于训练")
        print("当前数据分布:", sequence_counts)
    
    print("\n工作流程完成！")


if __name__ == "__main__":
    main()
