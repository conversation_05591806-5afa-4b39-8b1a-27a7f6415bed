#!/usr/bin/env python3
"""
医学影像nii文件预测演示

展示如何使用更新后的predict_nii.py进行MR序列分类预测
"""

import os
import sys
import tempfile
import shutil
import numpy as np
import SimpleITK as sitk

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.predict_nii import MRSequencePredictor, predict_nii_from_list


def create_demo_nii_file(output_path, sequence_type='t1', shape=(64, 64, 64)):
    """创建演示用的nii.gz文件，模拟不同MR序列的特征"""
    
    # 根据序列类型创建不同的信号特征
    if sequence_type == 't1':
        # T1加权像：脂肪高信号，水低信号
        base_intensity = 600
        noise_level = 100
    elif sequence_type == 't2':
        # T2加权像：水高信号，脂肪中等信号
        base_intensity = 800
        noise_level = 150
    elif sequence_type == 'flair':
        # FLAIR：抑制脑脊液信号
        base_intensity = 500
        noise_level = 80
    elif sequence_type == 'dwi':
        # DWI：弥散受限区域高信号
        base_intensity = 400
        noise_level = 120
    else:
        base_intensity = 500
        noise_level = 100
    
    # 创建3D数组
    arr = np.random.normal(base_intensity, noise_level, shape).astype(np.float32)
    
    # 添加一些解剖结构
    center_z, center_y, center_x = shape[0]//2, shape[1]//2, shape[2]//2
    
    # 创建椭球形结构（模拟脑组织）
    for z in range(shape[0]):
        for y in range(shape[1]):
            for x in range(shape[2]):
                # 椭球方程
                if ((z-center_z)/20)**2 + ((y-center_y)/25)**2 + ((x-center_x)/25)**2 < 1:
                    # 根据序列类型调整信号强度
                    if sequence_type == 't1':
                        arr[z, y, x] = base_intensity + 200 * np.sin(z/5)
                    elif sequence_type == 't2':
                        arr[z, y, x] = base_intensity + 300 * np.cos(y/5)
                    elif sequence_type == 'flair':
                        arr[z, y, x] = base_intensity + 150 * np.sin(x/5)
                    elif sequence_type == 'dwi':
                        arr[z, y, x] = base_intensity + 250 * np.cos(z/3)
    
    # 创建SimpleITK图像
    image = sitk.GetImageFromArray(arr)
    image.SetSpacing([1.0, 1.0, 1.0])
    image.SetOrigin([0.0, 0.0, 0.0])
    
    # 确保目录存在
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # 保存文件
    sitk.WriteImage(image, output_path)
    print(f"✓ 创建演示文件: {output_path} (序列: {sequence_type})")
    
    return output_path


def demo_single_prediction():
    """演示单文件预测"""
    print("=" * 60)
    print("单文件预测演示")
    print("=" * 60)
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 创建演示文件
        demo_file = os.path.join(temp_dir, "demo_t1.nii.gz")
        create_demo_nii_file(demo_file, sequence_type='t1')
        
        # 创建预测器（这里会尝试找到训练好的模型）
        try:
            predictor = MRSequencePredictor(slice_mode='middle')
            
            # 进行预测
            result = predictor.predict_nii(demo_file)
            
            print(f"\n预测结果:")
            print(f"文件: {os.path.basename(demo_file)}")
            if 'error' in result:
                print(f"❌ 预测失败: {result['error']}")
            else:
                print(f"🔬 预测序列: {result['predicted_class']}")
                print(f"📊 置信度: {result['confidence']:.4f}")
                print(f"⚙️  切片模式: {result['slice_mode']}")
                
                if 'probabilities' in result:
                    print("\n📈 概率分布:")
                    for seq_type, prob in result['probabilities'].items():
                        print(f"   {seq_type}: {prob:.4f}")
                        
        except FileNotFoundError as e:
            print(f"⚠️  未找到训练好的模型: {e}")
            print("请先训练模型或指定模型路径")
            
    finally:
        shutil.rmtree(temp_dir)


def demo_batch_prediction():
    """演示批量预测"""
    print("\n" + "=" * 60)
    print("批量预测演示")
    print("=" * 60)
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 创建多个演示文件
        demo_files = []
        sequences = ['t1', 't2', 'flair', 'dwi']
        
        for i, seq in enumerate(sequences):
            demo_file = os.path.join(temp_dir, f"demo_{seq}_{i:02d}.nii.gz")
            create_demo_nii_file(demo_file, sequence_type=seq)
            demo_files.append(demo_file)
        
        print(f"\n创建了 {len(demo_files)} 个演示文件")
        
        # 批量预测
        try:
            results = predict_nii_from_list(demo_files, slice_mode='middle')
            
            print(f"\n批量预测完成，共处理 {len(results)} 个文件")
            
            # 统计结果
            success_count = sum(1 for r in results if 'error' not in r)
            error_count = len(results) - success_count
            
            print(f"✓ 成功: {success_count}")
            print(f"❌ 失败: {error_count}")
            
            if success_count > 0:
                # 显示预测分布
                predictions = [r['predicted_class'] for r in results if 'error' not in r]
                from collections import Counter
                pred_counts = Counter(predictions)
                
                print(f"\n预测分布:")
                for seq_type, count in pred_counts.items():
                    print(f"  {seq_type}: {count} 个")
                    
        except FileNotFoundError as e:
            print(f"⚠️  未找到训练好的模型: {e}")
            print("请先训练模型或指定模型路径")
            
    finally:
        shutil.rmtree(temp_dir)


def demo_different_slice_modes():
    """演示不同切片模式的预测"""
    print("\n" + "=" * 60)
    print("不同切片模式预测演示")
    print("=" * 60)
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 创建演示文件
        demo_file = os.path.join(temp_dir, "demo_multi_mode.nii.gz")
        create_demo_nii_file(demo_file, sequence_type='t2')
        
        # 测试不同的切片模式
        modes = [
            ('middle', 'z'),
            ('multi', 'z'),
            ('multi', 'y'),
            ('multi', 'x')
        ]
        
        for slice_mode, axis in modes:
            print(f"\n测试模式: {slice_mode}, 轴向: {axis}")
            
            try:
                predictor = MRSequencePredictor(
                    slice_mode=slice_mode,
                    axis=axis
                )
                
                result = predictor.predict_nii(demo_file)
                
                if 'error' in result:
                    print(f"  ❌ 预测失败: {result['error']}")
                else:
                    print(f"  🔬 预测序列: {result['predicted_class']}")
                    print(f"  📊 置信度: {result['confidence']:.4f}")
                    
            except FileNotFoundError:
                print(f"  ⚠️  模式 {slice_mode}_{axis} 未找到对应模型")
                
    finally:
        shutil.rmtree(temp_dir)


def demo_command_line_usage():
    """演示命令行使用方法"""
    print("\n" + "=" * 60)
    print("命令行使用演示")
    print("=" * 60)
    
    print("基本用法:")
    print("python src/predict_nii.py -f path/to/image.nii.gz")
    
    print("\n指定模型:")
    print("python src/predict_nii.py -f path/to/image.nii.gz --model checkpoints/best_model.pth")
    
    print("\n使用multi模式:")
    print("python src/predict_nii.py -f path/to/image.nii.gz --slice_mode multi --axis z")
    
    print("\n批量预测:")
    print("python src/predict_nii.py -f file_list.json --verbose")
    
    print("\n显示详细信息:")
    print("python src/predict_nii.py -f path/to/image.nii.gz --verbose")


def main():
    """主演示函数"""
    print("医学影像nii文件预测演示")
    print("=" * 60)
    
    # 1. 单文件预测演示
    demo_single_prediction()
    
    # 2. 批量预测演示
    demo_batch_prediction()
    
    # 3. 不同切片模式演示
    demo_different_slice_modes()
    
    # 4. 命令行使用演示
    demo_command_line_usage()
    
    print("\n" + "=" * 60)
    print("演示完成！")
    print("=" * 60)
    
    print("\n主要功能:")
    print("✓ 支持单文件和批量预测")
    print("✓ 支持多种切片模式 (middle/multi)")
    print("✓ 支持多种轴向 (x/y/z)")
    print("✓ 自动模型检测")
    print("✓ 详细的预测结果")
    print("✓ 命令行接口")
    
    print("\n注意事项:")
    print("- 需要先训练好MR序列分类模型")
    print("- 模型文件应放在checkpoints目录下")
    print("- 支持的序列类型: t1, t2, flair, dwi")


if __name__ == "__main__":
    main()
