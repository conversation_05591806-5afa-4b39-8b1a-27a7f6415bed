# predict_nii.py 更新说明

## 🎯 更新目标

将原有的 `predict_nii.py` 从传统的图像分类预测更新为**医学影像MR序列分类预测**，适配新的3通道切片堆叠模型训练。

## ✅ 主要更新

### 1. 核心预测器类重构

**原有实现**：
```python
class ResNet50Predictor:
    # 使用cor_model和sag_model分别预测
    # 基于cut_nii_to_image_obj提取切片
    # 返回简单的0/1分类结果
```

**新实现**：
```python
class MRSequencePredictor:
    # 使用统一的MedicalImagePredictor
    # 基于3通道切片堆叠
    # 返回详细的MR序列分类结果（t1/t2/flair/dwi）
```

### 2. 预测功能升级

#### 单文件预测
```python
# 新的预测方法
predictor = MRSequencePredictor(
    model_path="checkpoints/resnet50_middle_z_best.pth",
    slice_mode='middle',  # 或 'multi'
    axis='z'
)

result = predictor.predict_nii("test.nii.gz")
# 返回详细结果：
{
    'file_path': 'test.nii.gz',
    'predicted_class': 't1',
    'predicted_class_idx': 0,
    'confidence': 0.892,
    'slice_mode': 'middle',
    'probabilities': {
        't1': 0.892,
        't2': 0.065,
        'flair': 0.032,
        'dwi': 0.011
    }
}
```

#### 批量预测
```python
# 支持文件列表批量预测
results = predict_nii_from_list(nii_files, slice_mode='middle')

# 支持JSON文件批量预测
predict_nii_from_json("file_list.json", slice_mode='middle')
```

### 3. 命令行接口增强

**新的命令行参数**：
```bash
# 基本预测
python src/predict_nii.py -f image.nii.gz

# 指定模型和切片模式
python src/predict_nii.py -f image.nii.gz \
    --model checkpoints/best_model.pth \
    --slice_mode middle \
    --axis z \
    --verbose

# 批量预测
python src/predict_nii.py -f file_list.json --verbose
```

**输出格式**：
```
📁 文件: patient001_brain.nii.gz
🔬 预测序列: t1
📊 置信度: 0.8924
⚙️  切片模式: middle

📈 概率分布:
   t1: 0.8924
   t2: 0.0651
   flair: 0.0321
   dwi: 0.0104
```

### 4. 技术架构更新

#### 数据处理流程
**原有流程**：
```
nii.gz → cut_nii_to_image_obj → cor/sag切片 → 分别预测 → 融合结果
```

**新流程**：
```
nii.gz → 3通道切片堆叠 → RGB图像 → 统一预测 → MR序列分类
```

#### 模型兼容性
- ✅ **自动模型检测**：自动查找最新的训练模型
- ✅ **多模式支持**：支持middle和multi切片模式
- ✅ **多轴向支持**：支持x、y、z三个轴向
- ✅ **向后兼容**：保留简化版预测接口

### 5. 错误处理和日志

#### 增强的错误处理
```python
# 文件读取错误
if 'error' in result:
    print(f"❌ 预测失败: {result['error']}")

# 模型加载错误
try:
    predictor = MRSequencePredictor(model_path="invalid_path.pth")
except FileNotFoundError as e:
    print(f"⚠️  未找到模型: {e}")
```

#### 详细的日志信息
```python
# 启用详细日志
python src/predict_nii.py -f image.nii.gz --verbose

# 日志输出示例
INFO: 找到模型文件: checkpoints/resnet50_middle_z_best.pth
INFO: 模型加载成功，设备: cuda
INFO: 预测结果 - 文件: image.nii.gz
INFO: 预测序列: t1
INFO: 置信度: 0.8924
```

## 🔧 使用示例

### 基本使用
```python
from src.predict_nii import MRSequencePredictor

# 创建预测器
predictor = MRSequencePredictor()

# 预测单个文件
result = predictor.predict_nii("brain_scan.nii.gz")
print(f"预测序列: {result['predicted_class']}")
```

### 高级使用
```python
# 指定切片模式和模型
predictor = MRSequencePredictor(
    model_path="checkpoints/custom_model.pth",
    slice_mode='multi',
    axis='z'
)

# 批量预测
nii_files = ["scan1.nii.gz", "scan2.nii.gz", "scan3.nii.gz"]
results = predict_nii_from_list(nii_files)

for result in results:
    if 'error' not in result:
        print(f"{result['file_path']}: {result['predicted_class']}")
```

### 命令行使用
```bash
# 单文件预测
python src/predict_nii.py -f brain_scan.nii.gz

# 指定参数
python src/predict_nii.py \
    -f brain_scan.nii.gz \
    --model checkpoints/resnet50_middle_z_best.pth \
    --slice_mode middle \
    --verbose

# 批量预测
python src/predict_nii.py -f scan_list.json
```

## 📊 性能对比

### 预测准确性
- **原有方式**：基于2个切面的简单分类
- **新方式**：基于3通道堆叠的深度特征提取
- **预期提升**：更丰富的特征信息，更高的分类准确性

### 处理效率
- **统一模型**：避免了多模型加载和推理
- **批量处理**：支持高效的批量预测
- **内存优化**：更好的内存管理

### 可扩展性
- **模块化设计**：易于添加新的序列类型
- **配置灵活**：支持多种切片模式和参数
- **接口标准**：统一的预测接口

## 🔄 迁移指南

### 从旧版本迁移

**旧代码**：
```python
from src.predict_nii import ResNet50Predictor

predictor = ResNet50Predictor()
result = predictor.predict_nii("image.nii.gz")  # 返回0或1
```

**新代码**：
```python
from src.predict_nii import MRSequencePredictor

predictor = MRSequencePredictor()
result = predictor.predict_nii("image.nii.gz")  # 返回详细结果
predicted_class = result['predicted_class']  # 't1', 't2', 'flair', 'dwi'
```

### 保持向后兼容
```python
# 如果需要简单的索引结果
simple_result = predictor.predict_nii_simple("image.nii.gz")  # 返回0,1,2,3
```

## 🎉 总结

这次更新将 `predict_nii.py` 从简单的二分类工具升级为功能完整的**MR序列分类预测系统**：

1. **功能升级**：从简单分类到专业的MR序列识别
2. **架构优化**：统一的3通道处理流程
3. **接口增强**：更丰富的命令行参数和返回结果
4. **性能提升**：更高的预测准确性和处理效率
5. **易用性**：自动模型检测和详细的错误提示

现在您可以直接使用更新后的 `predict_nii.py` 进行医学影像MR序列分类预测！🚀
