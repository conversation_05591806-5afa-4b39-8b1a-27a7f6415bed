#!/usr/bin/env python3
"""
快速数据拆分脚本

这是一个简化版本的数据拆分脚本，用于快速将医学影像数据拆分为训练/验证/测试集。
适用于快速原型开发和测试。
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from scripts.split_medical_data import MedicalDataSplitter


def quick_split(source_dir="data/medical", target_dir="data/medical", 
                train_ratio=0.7, val_ratio=0.15, test_ratio=0.15,
                copy_mode='symlink', dry_run=False):
    """
    快速拆分医学影像数据
    
    Args:
        source_dir (str): 源数据目录
        target_dir (str): 目标数据目录
        train_ratio (float): 训练集比例
        val_ratio (float): 验证集比例
        test_ratio (float): 测试集比例
        copy_mode (str): 复制模式，'copy', 'symlink', 'hardlink'
        dry_run (bool): 是否只显示计划而不执行
    """
    print("快速医学影像数据拆分")
    print("=" * 40)
    print(f"源目录: {source_dir}")
    print(f"目标目录: {target_dir}")
    print(f"拆分比例: 训练集 {train_ratio}, 验证集 {val_ratio}, 测试集 {test_ratio}")
    print(f"复制模式: {copy_mode}")
    print(f"DRY RUN: {dry_run}")
    print()
    
    # 创建拆分器
    splitter = MedicalDataSplitter(
        source_dir=source_dir,
        target_dir=target_dir,
        train_ratio=train_ratio,
        val_ratio=val_ratio,
        test_ratio=test_ratio
    )
    
    if dry_run:
        print("DRY RUN 模式 - 仅显示拆分计划")
        print("-" * 30)
        
        try:
            splitter.discover_sequences()
            splitter.validate_data()
            split_info = splitter.split_files(strategy='random', seed=42)
            splitter.generate_summary(split_info)
            print("\n✓ DRY RUN 完成")
            return True
        except Exception as e:
            print(f"\n✗ DRY RUN 失败: {e}")
            return False
    else:
        # 执行实际拆分
        success = splitter.run(
            strategy='random',
            copy_mode=copy_mode,
            seed=42
        )
        return success


def main():
    """主函数 - 提供交互式界面"""
    print("医学影像数据快速拆分工具")
    print("=" * 50)
    
    # 检查当前数据结构
    source_dir = input("请输入源数据目录 (默认: data/medical): ") or "data/medical"
    if not os.path.exists(source_dir):
        print(f"错误: 源目录 {source_dir} 不存在")
        print("请确保您的数据位于 data/medical/ 目录下")
        return
    
    # 显示当前数据结构
    print(f"当前数据目录结构:")
    for item in Path(source_dir).iterdir():
        if item.is_dir() and not item.name.startswith('.'):
            nii_files = list(item.glob("*.nii.gz")) + list(item.glob("*.nii"))
            print(f"  {item.name}/: {len(nii_files)} 个文件")
    
    print()
    
    # 询问用户选择
    print("请选择操作:")
    print("1. 预览拆分计划 (DRY RUN)")
    print("2. 执行拆分 (使用软链接)")
    print("3. 执行拆分 (复制文件)")
    print("4. 自定义拆分")
    print("0. 退出")
    
    choice = input("\n请输入选择 (0-4): ").strip()
    
    if choice == '0':
        print("退出")
        return
    elif choice == '1':
        # 预览拆分计划
        quick_split(source_dir=source_dir, dry_run=True)
    elif choice == '2':
        # 使用软链接执行拆分
        print("\n使用软链接模式拆分数据...")
        success = quick_split(source_dir, copy_mode='symlink')
        if success:
            print("\n✓ 拆分完成！数据已准备好用于训练。")
        else:
            print("\n✗ 拆分失败")
    elif choice == '3':
        # 复制文件执行拆分
        print("\n使用文件复制模式拆分数据...")
        success = quick_split(source_dir, copy_mode='copy')
        if success:
            print("\n✓ 拆分完成！数据已准备好用于训练。")
        else:
            print("\n✗ 拆分失败")
    elif choice == '4':
        # 自定义拆分
        print("\n自定义拆分设置:")
        try:
            train_ratio = float(input("训练集比例 (默认 0.7): ") or "0.7")
            val_ratio = float(input("验证集比例 (默认 0.15): ") or "0.15")
            test_ratio = float(input("测试集比例 (默认 0.15): ") or "0.15")
            
            if abs(train_ratio + val_ratio + test_ratio - 1.0) > 1e-6:
                print("错误: 比例之和必须为 1.0")
                return
            
            copy_mode = input("复制模式 (copy/symlink/hardlink, 默认 symlink): ") or "symlink"
            if copy_mode not in ['copy', 'symlink', 'hardlink']:
                print("错误: 无效的复制模式")
                return
            
            dry_run = input("是否只预览 (y/n, 默认 n): ").lower().startswith('y')
            
            success = quick_split(
                source_dir=source_dir,
                train_ratio=train_ratio,
                val_ratio=val_ratio,
                test_ratio=test_ratio,
                copy_mode=copy_mode,
                dry_run=dry_run
            )
            
            if success and not dry_run:
                print("\n✓ 自定义拆分完成！")
            
        except ValueError as e:
            print(f"错误: 输入无效 - {e}")
    else:
        print("无效选择")


if __name__ == "__main__":
    main()
