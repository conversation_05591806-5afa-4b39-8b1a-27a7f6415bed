# 医学影像数据拆分工具

这个目录包含了用于将医学影像数据从原始格式转换为训练标准格式的脚本。

## 📁 当前数据结构

您的数据当前位于：
```
data/medical/
├── t1/
│   └── sample_001.nii.gz
├── t2/
│   └── sample_001.nii.gz
├── t3/
│   └── sample_001.nii.gz
├── flair/
│   └── sample_001.nii.gz
└── dwi/
    └── sample_001.nii.gz
```

## 🎯 目标格式

训练需要的标准格式：
```
data/medical/
├── train/
│   ├── t1/
│   │   ├── sample_001.nii.gz
│   │   └── ...
│   ├── t2/
│   │   ├── sample_001.nii.gz
│   │   └── ...
│   └── t3/
│       ├── sample_001.nii.gz
│       └── ...
├── val/
│   ├── t1/
│   ├── t2/
│   └── t3/
└── test/
    ├── t1/
    ├── t2/
    └── t3/
```

## 🚀 快速开始

### 方法1: 使用交互式脚本（推荐）

```bash
python scripts/quick_split.py
```

这个脚本提供了友好的交互界面，可以：
- 预览拆分计划
- 选择不同的复制模式
- 自定义拆分比例

### 方法2: 使用命令行脚本

```bash
# 预览拆分计划（不实际执行）
python scripts/split_medical_data.py --dry-run

# 执行拆分（使用软链接，推荐）
python scripts/split_medical_data.py --copy-mode symlink

# 执行拆分（复制文件）
python scripts/split_medical_data.py --copy-mode copy

# 自定义拆分比例
python scripts/split_medical_data.py --train-ratio 0.8 --val-ratio 0.1 --test-ratio 0.1
```

## 📋 脚本说明

### `split_medical_data.py`
完整功能的数据拆分脚本，支持：
- 自动发现序列类型
- 多种拆分策略（随机、按文件名排序）
- 多种复制模式（复制、软链接、硬链接）
- 数据完整性验证
- 详细的拆分报告

### `quick_split.py`
简化版本的拆分脚本，提供：
- 交互式界面
- 快速拆分选项
- 适合快速原型开发

## ⚙️ 参数说明

### 拆分比例
- `--train-ratio`: 训练集比例（默认: 0.7）
- `--val-ratio`: 验证集比例（默认: 0.15）
- `--test-ratio`: 测试集比例（默认: 0.15）

### 复制模式
- `copy`: 复制文件（占用更多磁盘空间，但更安全）
- `symlink`: 创建软链接（推荐，节省空间）
- `hardlink`: 创建硬链接（节省空间，但删除原文件会影响链接）

### 拆分策略
- `random`: 随机拆分（默认）
- `sorted`: 按文件名排序后拆分

## 🔍 使用示例

### 示例1: 快速拆分（推荐）
```bash
# 运行交互式脚本
python scripts/quick_split.py

# 选择选项2或3进行拆分
```

### 示例2: 预览拆分计划
```bash
python scripts/split_medical_data.py --dry-run
```

### 示例3: 自定义拆分
```bash
python scripts/split_medical_data.py \
    --train-ratio 0.8 \
    --val-ratio 0.1 \
    --test-ratio 0.1 \
    --copy-mode symlink \
    --strategy random
```

## 📊 拆分后验证

拆分完成后，脚本会生成 `data/medical/split_summary.txt` 文件，包含详细的拆分信息。

您也可以手动检查：
```bash
# 检查目录结构
ls -la data/medical/train/
ls -la data/medical/val/
ls -la data/medical/test/

# 统计文件数量
find data/medical/train -name "*.nii.gz" | wc -l
find data/medical/val -name "*.nii.gz" | wc -l
find data/medical/test -name "*.nii.gz" | wc -l
```

## 🏃‍♂️ 拆分后开始训练

数据拆分完成后，您可以开始训练：

```bash
# 使用医学影像训练脚本
python src/train_medical.py

# 或者运行完整的工作流程示例
python examples/complete_medical_workflow.py
```

## ⚠️ 注意事项

1. **备份数据**: 建议在拆分前备份原始数据
2. **磁盘空间**: 使用 `copy` 模式会占用额外磁盘空间
3. **文件权限**: 确保对数据目录有读写权限
4. **数据量**: 每个序列类型至少需要3个文件才能进行有效拆分

## 🐛 故障排除

### 常见问题

1. **"源目录不存在"**
   - 检查 `data/medical/` 目录是否存在
   - 确保数据文件位于正确位置

2. **"未找到nii.gz文件"**
   - 检查文件扩展名是否正确（.nii.gz 或 .nii）
   - 确保文件不是空的

3. **"权限被拒绝"**
   - 检查文件和目录权限
   - 使用 `chmod` 修改权限

4. **软链接失效**
   - 确保原始文件路径没有改变
   - 考虑使用 `copy` 模式代替 `symlink`

### 获取帮助

```bash
python scripts/split_medical_data.py --help
```

## 📝 更新日志

- v1.0: 初始版本，支持基本的数据拆分功能
- v1.1: 添加交互式界面和多种复制模式
- v1.2: 增强数据验证和错误处理
