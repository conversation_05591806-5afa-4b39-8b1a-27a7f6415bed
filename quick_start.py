#!/usr/bin/env python3
"""
医学影像MR序列分类快速开始脚本

这个脚本提供了一个简单的入口点来快速开始使用医学影像数据加载器。
运行此脚本将：
1. 检查环境和依赖
2. 创建示例数据目录结构
3. 演示数据加载功能
4. 提供下一步指导
"""

import os
import sys
import subprocess

def check_dependencies():
    """检查必要的依赖包"""
    print("检查依赖包...")
    
    required_packages = [
        'torch',
        'torchvision', 
        'SimpleITK',
        'numpy',
        'PIL',
        'matplotlib',
        'sklearn'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺失的包: {missing_packages}")
        print("请运行以下命令安装:")
        if 'SimpleITK' in missing_packages:
            print("pip install SimpleITK")
        if 'sklearn' in missing_packages:
            print("pip install scikit-learn")
        for pkg in missing_packages:
            if pkg not in ['SimpleITK', 'sklearn']:
                print(f"pip install {pkg}")
        return False
    
    print("✓ 所有依赖包已安装")
    return True


def create_sample_structure():
    """创建示例数据目录结构"""
    print("\n创建示例数据目录结构...")
    
    base_dir = "data/medical"
    sequences = ['t1', 't2', 'flair', 'dwi']
    splits = ['train', 'val', 'test']
    
    created_dirs = []
    
    for split in splits:
        for seq in sequences:
            dir_path = os.path.join(base_dir, split, seq)
            if not os.path.exists(dir_path):
                os.makedirs(dir_path, exist_ok=True)
                created_dirs.append(dir_path)
    
    if created_dirs:
        print("✓ 已创建以下目录:")
        for dir_path in created_dirs[:5]:  # 只显示前5个
            print(f"  {dir_path}")
        if len(created_dirs) > 5:
            print(f"  ... 和其他 {len(created_dirs) - 5} 个目录")
    else:
        print("✓ 目录结构已存在")
    
    return base_dir


def demonstrate_usage():
    """演示基本使用方法"""
    print("\n演示数据加载器使用方法...")
    
    try:
        from config.config import Config
        from src.data_loader import MedicalImageDataset, get_medical_data_loaders
        
        config = Config()
        
        # 检查是否有实际的nii.gz文件
        data_dir = "data/medical/train"
        nii_files = []
        
        if os.path.exists(data_dir):
            for root, _, files in os.walk(data_dir):
                for file in files:
                    if file.endswith('.nii.gz') or file.endswith('.nii'):
                        nii_files.append(os.path.join(root, file))
        
        if nii_files:
            print(f"✓ 找到 {len(nii_files)} 个医学影像文件")
            
            # 创建数据集
            dataset = MedicalImageDataset(
                data_dir=data_dir,
                slice_mode='middle'
            )
            
            print(f"✓ 数据集大小: {len(dataset)}")
            print(f"✓ 类别: {dataset.get_class_names()}")
            
            if len(dataset) > 0:
                # 获取一个样本
                image, label = dataset[0]
                print(f"✓ 样本图像尺寸: {image.size}")
                print(f"✓ 样本标签: {label}")
            
            # 演示数据加载器
            try:
                data_loaders, class_names, dataset_sizes = get_medical_data_loaders(
                    config, slice_mode='middle'
                )
                print(f"✓ 成功创建数据加载器")
                print(f"✓ 数据集大小: {dataset_sizes}")
            except Exception as e:
                print(f"✗ 数据加载器创建失败: {e}")
        else:
            print("ℹ 未找到nii.gz文件，请将医学影像文件放入相应目录")
            print("  示例文件路径:")
            print("    data/medical/train/t1/patient001.nii.gz")
            print("    data/medical/train/t2/patient001.nii.gz")
            print("    data/medical/train/flair/patient001.nii.gz")
            print("    data/medical/train/dwi/patient001.nii.gz")
        
    except ImportError as e:
        print(f"✗ 导入错误: {e}")
        print("请确保项目结构正确")
    except Exception as e:
        print(f"✗ 演示失败: {e}")


def show_next_steps():
    """显示下一步指导"""
    print("\n" + "=" * 60)
    print("下一步指导")
    print("=" * 60)
    
    print("\n1. 准备数据:")
    print("   将您的nii.gz文件放入相应的目录中:")
    print("   - T1序列 → data/medical/train/t1/")
    print("   - T2序列 → data/medical/train/t2/")
    print("   - FLAIR序列 → data/medical/train/flair/")
    print("   - DWI序列 → data/medical/train/dwi/")
    
    print("\n2. 测试数据加载:")
    print("   python examples/medical_dataset_example.py")
    
    print("\n3. 运行完整工作流程:")
    print("   python examples/complete_medical_workflow.py")
    
    print("\n4. 训练模型:")
    print("   python src/train_medical.py")
    
    print("\n5. 运行测试:")
    print("   python tests/test_medical_dataset.py")
    
    print("\n6. 查看文档:")
    print("   docs/medical_dataset_usage.md")
    
    print("\n支持的切片模式:")
    print("   - 'middle': 提取xyz面的中间切片")
    print("   - 'multi': 提取指定轴的25%、50%、75%位置切片")
    
    print("\n支持的轴向 (multi模式):")
    print("   - 'z': Z轴 (轴向面)")
    print("   - 'y': Y轴 (冠状面)")
    print("   - 'x': X轴 (矢状面)")


def main():
    """主函数"""
    print("=" * 60)
    print("医学影像MR序列分类 - 快速开始")
    print("=" * 60)
    
    # 1. 检查依赖
    if not check_dependencies():
        print("\n请先安装缺失的依赖包，然后重新运行此脚本")
        return
    
    # 2. 创建目录结构
    base_dir = create_sample_structure()
    
    # 3. 演示使用方法
    demonstrate_usage()
    
    # 4. 显示下一步指导
    show_next_steps()
    
    print(f"\n✓ 快速开始完成！")
    print(f"数据目录: {os.path.abspath(base_dir)}")


if __name__ == "__main__":
    main()
