import io
import json
import numpy as np
from fastapi import FastAPI, HTTPException, File, UploadFile
from pydantic import BaseModel, Field
from fastapi.middleware.cors import CORSMiddleware
from typing import List
from PIL import Image
from .predict import predictor
import traceback
import os
import logging

BASE_DIR = os.path.dirname(os.path.abspath('__file__'))


# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
# ==============================================================================
# 1. 应用初始化和模型加载
# ==============================================================================

# 初始化 FastAPI 应用
app = FastAPI(
    title="图像预测 API",
    description="一个使用  ResNet-50 模型进行MR图像分类的 FastAPI 服务。",
    version="1.0.0",
)

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.on_event("startup")
async def startup_event():
    """应用启动时预加载模型"""
    logger.info("Starting up the application...")
    predictor.load_model()
    logger.info("Application startup completed")
# ==============================================================================
# 2. 定义数据模型 (Pydantic)
# ==============================================================================
class PredictRequest(BaseModel):
    mode: str = Field(..., description='muddle or multi')


class PredictionResult(BaseModel):
    predicted_class: str
    mode: str
    confidence: float


# 定义最终的响应体模型
class PredictionResponse(BaseModel):
    code: int = Field(..., description='200')
    result: PredictionResult


# ==============================================================================
# 3. 创建 API 端点
# ==============================================================================
def load_version():
    try:
        with open(os.path.join(BASE_DIR, 'checkpoints', 'version.json'), 'r') as f:
            version = json.load(f)
            return version['version']
    except Exception  as e:
        print(traceback.format_exc())
        return 'not version info'

version = load_version()

@app.post("/predict", response_model=PredictionResponse,)
async def predict_images(array_file: UploadFile = File(...), item = PredictRequest):
    """
    接收两个图像数组并返回它们的预测结果。
    线程池中运行它，从而实现并发，不会阻塞服务器的主事件循环。
    """
    try:
        mode = item.mode
        arry_contents = await  array_file.read()
        # 2. 从内存中的二进制数据加载NumPy数组

        buffer = io.BytesIO(arry_contents)
        buffer.seek(0)
        received_array = np.load(buffer)

        image_obj = Image.fromarray(received_array)
        result = predictor.predict_score(image_obj, mode=mode)
    except RuntimeError as e:
        # 如果 get_prediction 内部发生错误，返回 500 错误
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        print(traceback.format_exc())
        # 捕获其他可能的错误，例如 Pydantic 验证失败等
        raise HTTPException(status_code=400, detail=f"请求处理失败: {e}")

    return PredictionResponse(code=200, result=result, version=version)


# ==============================================================================
# 5. (可选) 添加一个根路径用于健康检查
# ==============================================================================
@app.get("/")
def read_root():
    return {"status": "ok", "message": "欢迎使用 ResNet-50 图像预测 API"}